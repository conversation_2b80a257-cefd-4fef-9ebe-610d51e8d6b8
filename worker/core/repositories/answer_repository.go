package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type AnswerRepository interface {
	Repository[*models.Answer]
}

type answerRepository struct {
	*GormRepository[*models.Answer]
}

func NewAnswerRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) AnswerRepository {
	return &answerRepository{
		GormRepository: NewGormRepository[*models.Answer](db, "answer", queueDispatcher),
	}
}
