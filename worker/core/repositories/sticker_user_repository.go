package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type StickerUserRepository interface {
	Repository[*models.StickerUser]
}

type stickerUserRepository struct {
	*GormRepository[*models.StickerUser]
}

func NewStickerUserRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) StickerUserRepository {
	return &stickerUserRepository{
		GormRepository: NewGormRepository[*models.StickerUser](db, "stickerUsers", queueDispatcher),
	}
}
