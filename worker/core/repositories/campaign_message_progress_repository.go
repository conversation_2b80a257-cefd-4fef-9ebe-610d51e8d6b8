package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type CampaignMessageProgressRepository interface {
	Repository[*models.CampaignMessageProgress]
}

type campaignMessageProgressRepository struct {
	*GormRepository[*models.CampaignMessageProgress]
}

func NewCampaignMessageProgressRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) CampaignMessageProgressRepository {
	return &campaignMessageProgressRepository{
		GormRepository: NewGormRepository[*models.CampaignMessageProgress](db, "campaignMessageProgress", queueDispatcher),
	}
}
