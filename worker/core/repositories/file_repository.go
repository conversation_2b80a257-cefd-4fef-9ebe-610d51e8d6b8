package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type FileRepository interface {
	Repository[*models.File]
}

type fileRepository struct {
	*GormRepository[*models.File]
}

func NewFileRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) FileRepository {
	return &fileRepository{
		GormRepository: NewGormRepository[*models.File](db, "file", queueDispatcher),
	}
}
