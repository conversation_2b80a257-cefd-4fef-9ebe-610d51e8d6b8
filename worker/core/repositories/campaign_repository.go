package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type CampaignRepository interface {
	Repository[*models.Campaign]
}

type campaignRepository struct {
	*GormRepository[*models.Campaign]
}

func NewCampaignRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) CampaignRepository {
	return &campaignRepository{
		GormRepository: NewGormRepository[*models.Campaign](db, "campaign", queueDispatcher),
	}
}
