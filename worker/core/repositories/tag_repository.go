package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type TagRepository interface {
	Repository[*models.Tag]
}

type tagRepository struct {
	*GormRepository[*models.Tag]
}

func NewTagRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) TagRepository {
	return &tagRepository{
		GormRepository: NewGormRepository[*models.Tag](db, "tag", queueDispatcher),
	}
}
