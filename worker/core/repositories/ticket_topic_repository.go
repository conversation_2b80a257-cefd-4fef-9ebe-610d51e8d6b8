package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type TicketTopicRepository interface {
	Repository[*models.TicketTopic]
}

type ticketTopicRepository struct {
	*GormRepository[*models.TicketTopic]
}

func NewTicketTopicRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) TicketTopicRepository {
	return &ticketTopicRepository{
		GormRepository: NewGormRepository[*models.TicketTopic](db, "ticketTopics", queueDispatcher),
	}
}
