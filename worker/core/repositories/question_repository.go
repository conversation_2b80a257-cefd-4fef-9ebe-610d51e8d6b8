package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type QuestionRepository interface {
	Repository[*models.Question]
}

type questionRepository struct {
	*GormRepository[*models.Question]
}

func NewQuestionRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) QuestionRepository {
	return &questionRepository{
		GormRepository: NewGormRepository[*models.Question](db, "question", queueDispatcher),
	}
}
