package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type CampaignMessageRepository interface {
	Repository[*models.CampaignMessage]
}

type campaignMessageRepository struct {
	*GormRepository[*models.CampaignMessage]
}

func NewCampaignMessageRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) CampaignMessageRepository {
	return &campaignMessageRepository{
		GormRepository: NewGormRepository[*models.CampaignMessage](db, "campaignMessage", queueDispatcher),
	}
}
