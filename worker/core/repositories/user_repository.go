package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type UserRepository interface {
	Repository[*models.User]
}

type userRepository struct {
	*GormRepository[*models.User]
}

func NewUserRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) UserRepository {
	return &userRepository{
		GormRepository: NewGormRepository[*models.User](db, "user", queueDispatcher),
	}
}
