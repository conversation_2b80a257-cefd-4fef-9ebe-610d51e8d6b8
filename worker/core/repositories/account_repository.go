package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type AccountRepository interface {
	Repository[*models.Account]
}

type accountRepository struct {
	*GormRepository[*models.Account]
}

func NewAccountRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) AccountRepository {
	return &accountRepository{
		GormRepository: NewGormRepository[*models.Account](db, "account", queueDispatcher),
	}
}
