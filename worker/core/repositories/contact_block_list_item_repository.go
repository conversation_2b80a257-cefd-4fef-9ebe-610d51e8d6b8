package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type ContactBlockListItemRepository interface {
	Repository[*models.ContactBlockListItem]
}

type contactBlockListItemRepository struct {
	*GormRepository[*models.ContactBlockListItem]
}

func NewContactBlockListItemRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) ContactBlockListItemRepository {
	return &contactBlockListItemRepository{
		GormRepository: NewGormRepository[*models.ContactBlockListItem](db, "contactBlockListItem", queueDispatcher),
	}
}
