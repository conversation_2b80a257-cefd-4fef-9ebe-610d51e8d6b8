package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type TicketTransferRepository interface {
	Repository[*models.TicketTransfer]
}

type ticketTransferRepository struct {
	*GormRepository[*models.TicketTransfer]
}

func NewTicketTransferRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) TicketTransferRepository {
	return &ticketTransferRepository{
		GormRepository: NewGormRepository[*models.TicketTransfer](db, "ticketTransfers", queueDispatcher),
	}
}
