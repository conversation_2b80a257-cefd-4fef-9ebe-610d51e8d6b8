package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type WhatsappBusinessTemplateHistoryRepository interface {
	Repository[*models.WhatsappBusinessTemplateHistory]
}

type whatsappBusinessTemplateHistoryRepository struct {
	*GormRepository[*models.WhatsappBusinessTemplateHistory]
}

func NewWhatsappBusinessTemplateHistoryRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) WhatsappBusinessTemplateHistoryRepository {
	return &whatsappBusinessTemplateHistoryRepository{
		GormRepository: NewGormRepository[*models.WhatsappBusinessTemplateHistory](db, "whatsappBusinessTemplateHistory", queueDispatcher),
	}
}
