package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type WhatsappBusinessTemplateRepository interface {
	Repository[*models.WhatsappBusinessTemplate]
}

type whatsappBusinessTemplateRepository struct {
	*GormRepository[*models.WhatsappBusinessTemplate]
}

func NewWhatsappBusinessTemplateRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) WhatsappBusinessTemplateRepository {
	return &whatsappBusinessTemplateRepository{
		GormRepository: NewGormRepository[*models.WhatsappBusinessTemplate](db, "whatsappBusinessTemplate", queueDispatcher),
	}
}
