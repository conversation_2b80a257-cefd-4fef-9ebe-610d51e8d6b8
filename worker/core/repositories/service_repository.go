package repositories

import (
	"sync"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type ServiceRepository interface {
	Repository[*models.Service]
}

type serviceRepository struct {
	*GormRepository[*models.Service]
}

func NewServiceRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) ServiceRepository {
	return &serviceRepository{
		GormRepository: NewGormRepository[*models.Service](db, "service", queueDispatcher),
	}
}

var serviceRepositoryInstance ServiceRepository
var onceServiceRepository sync.Once

func GetServiceRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) ServiceRepository {
	onceServiceRepository.Do(func() {
		serviceRepositoryInstance = NewServiceRepository(db, queueDispatcher)
	})
	return serviceRepositoryInstance
}
