package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type LinkRepository interface {
	Repository[*models.Link]
}

type linkRepository struct {
	*GormRepository[*models.Link]
}

func NewLinkRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) LinkRepository {
	return &linkRepository{
		GormRepository: NewGormRepository[*models.Link](db, "links", queueDispatcher),
	}
}
