package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type StickerRepository interface {
	Repository[*models.Sticker]
}

type stickerRepository struct {
	*GormRepository[*models.Sticker]
}

func NewStickerRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) StickerRepository {
	return &stickerRepository{
		GormRepository: NewGormRepository[*models.Sticker](db, "stickers", queueDispatcher),
	}
}
