package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type ContactRepository interface {
	Repository[*models.Contact]
}

type contactRepository struct {
	*GormRepository[*models.Contact]
}

func NewContactRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) ContactRepository {
	return &contactRepository{
		GormRepository: NewGormRepository[*models.Contact](db, "contact", queueDispatcher),
	}
}
