package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type OrganizationRepository interface {
	Repository[*models.Organization]
}

type organizationRepository struct {
	*GormRepository[*models.Organization]
}

func NewOrganizationRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) OrganizationRepository {
	return &organizationRepository{
		GormRepository: NewGormRepository[*models.Organization](db, "organization", queueDispatcher),
	}
}
