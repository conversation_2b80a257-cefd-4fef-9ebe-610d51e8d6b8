package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type ContactBlockListControlRepository interface {
	Repository[*models.ContactBlockListControl]
}

type contactBlockListControlRepository struct {
	*GormRepository[*models.ContactBlockListControl]
}

func NewContactBlockListControlRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) ContactBlockListControlRepository {
	return &contactBlockListControlRepository{
		GormRepository: NewGormRepository[*models.ContactBlockListControl](db, "contactBlockListControl", queueDispatcher),
	}
}
