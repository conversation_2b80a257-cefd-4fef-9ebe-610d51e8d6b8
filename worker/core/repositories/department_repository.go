package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type DepartmentRepository interface {
	Repository[*models.Department]
}

type departmentRepository struct {
	*GormRepository[*models.Department]
}

func NewDepartmentRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) DepartmentRepository {
	return &departmentRepository{
		GormRepository: NewGormRepository[*models.Department](db, "department", queueDispatcher),
	}
}
