package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type MessageRepository interface {
	Repository[*models.Message]
}

type messageRepository struct {
	*GormRepository[*models.Message]
}

func NewMessageRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) MessageRepository {
	return &messageRepository{
		GormRepository: NewGormRepository[*models.Message](db, "message", queueDispatcher),
	}
}
