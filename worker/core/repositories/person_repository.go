package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type PersonRepository interface {
	Repository[*models.Person]
}

type personRepository struct {
	*GormRepository[*models.Person]
}

func NewPersonRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) PersonRepository {
	return &personRepository{
		GormRepository: NewGormRepository[*models.Person](db, "person", queueDispatcher),
	}
}
