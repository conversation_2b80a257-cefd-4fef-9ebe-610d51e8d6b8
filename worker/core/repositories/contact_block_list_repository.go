package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type ContactBlockListRepository interface {
	Repository[*models.ContactBlockList]
}

type contactBlockListRepository struct {
	*GormRepository[*models.ContactBlockList]
}

func NewContactBlockListRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) ContactBlockListRepository {
	return &contactBlockListRepository{
		GormRepository: NewGormRepository[*models.ContactBlockList](db, "contactBlockList", queueDispatcher),
	}
}
