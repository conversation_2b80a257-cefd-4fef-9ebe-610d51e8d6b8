package repositories

import (
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/dispatcher/queue"

	"gorm.io/gorm"
)

type TicketRepository interface {
	Repository[*models.Ticket]
}

type ticketRepository struct {
	*GormRepository[*models.Ticket]
}

func NewTicketRepository(db *gorm.DB, queueDispatcher *queue.QueueJobsDispatcherService) TicketRepository {
	return &ticketRepository{
		GormRepository: NewGormRepository[*models.Ticket](db, "ticket", queueDispatcher),
	}
}
