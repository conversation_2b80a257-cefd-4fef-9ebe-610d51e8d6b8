package models

import (
	"bytes"
	"github.com/bytedance/sonic"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"reflect"
	"strings"
)

// isEmptyJSON checks if a JSON string represents an empty object or array
func isEmptyJSON(jsonStr string) bool {
	trimmed := strings.TrimSpace(jsonStr)
	return trimmed == "{}" || trimmed == "[]" || trimmed == "null"
}

// Diff compares this AccountFlags instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *AccountFlags) Diff(old *AccountFlags) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare DisableHsmLimit

	// Simple type comparison
	if new.DisableHsmLimit != old.DisableHsmLimit {
		diff["disable-hsm-limit"] = new.DisableHsmLimit
	}

	return diff
}

// Diff compares this AccountAiPlan instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *AccountAiPlan) Diff(old *AccountAiPlan) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Copilot

	// Simple type comparison
	if new.Copilot != old.Copilot {
		diff["copilot"] = new.Copilot
	}

	// Compare Summary

	// Simple type comparison
	if new.Summary != old.Summary {
		diff["summary"] = new.Summary
	}

	// Compare MagicText

	// Simple type comparison
	if new.MagicText != old.MagicText {
		diff["magic-text"] = new.MagicText
	}

	// Compare Transcription

	// Simple type comparison
	if new.Transcription != old.Transcription {
		diff["transcription"] = new.Transcription
	}

	return diff
}

// Diff compares this AccountPlan instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *AccountPlan) Diff(old *AccountPlan) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare HsmUsedLimit

	// Simple type comparison
	if new.HsmUsedLimit != old.HsmUsedLimit {
		diff["hsmUsedLimit"] = new.HsmUsedLimit
	}

	// Compare HsmLimit

	// Simple type comparison
	if new.HsmLimit != old.HsmLimit {
		diff["hsmLimit"] = new.HsmLimit
	}

	// Compare Services

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Services, old.Services) {
		diff["services"] = new.Services
	}

	// Compare Ai

	// Simple type comparison
	if new.Ai != old.Ai {
		diff["ai"] = new.Ai
	}

	// Compare Users

	// Simple type comparison
	if new.Users != old.Users {
		diff["users"] = new.Users
	}

	// Compare EmailNotifications

	// Simple type comparison
	if new.EmailNotifications != old.EmailNotifications {
		diff["emailNotifications"] = new.EmailNotifications
	}

	return diff
}

// Diff compares this AccountCampaignSettings instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *AccountCampaignSettings) Diff(old *AccountCampaignSettings) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare AutoPauseMode

	// Simple type comparison
	if new.AutoPauseMode != old.AutoPauseMode {
		diff["auto-pause-mode"] = new.AutoPauseMode
	}

	return diff
}

// Diff compares this AccountSettings instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *AccountSettings) Diff(old *AccountSettings) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare DisableDefaultTicketTransfer

	// Simple type comparison
	if new.DisableDefaultTicketTransfer != old.DisableDefaultTicketTransfer {
		diff["disableDefaultTicketTransfer"] = new.DisableDefaultTicketTransfer
	}

	// Compare TicketsEnabled

	// Simple type comparison
	if new.TicketsEnabled != old.TicketsEnabled {
		diff["ticketsEnabled"] = new.TicketsEnabled
	}

	// Compare ProtocolFormat

	// Simple type comparison
	if new.ProtocolFormat != old.ProtocolFormat {
		diff["string"] = new.ProtocolFormat
	}

	// Compare EncryptionDisabled

	// Simple type comparison
	if new.EncryptionDisabled != old.EncryptionDisabled {
		diff["encryptionDisabled"] = new.EncryptionDisabled
	}

	// Compare Flags

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Flags == nil && old.Flags != nil {
		// new is nil, old is not nil - set to null
		diff["flags"] = nil
	} else if new.Flags != nil && old.Flags == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Flags)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["flags"] = gorm.Expr("? || ?", clause.Column{Name: "flags"}, string(jsonValue))
		} else if err != nil {
			diff["flags"] = new.Flags
		}
	} else if new.Flags != nil && old.Flags != nil {
		// Both are not nil - use attribute-by-attribute diff
		FlagsDiff := new.Flags.Diff(old.Flags)
		if len(FlagsDiff) > 0 {
			jsonValue, err := sonic.Marshal(FlagsDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["flags"] = gorm.Expr("? || ?", clause.Column{Name: "flags"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["flags"] = new.Flags
			}
		}
	}

	// Compare Campaign

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Campaign == nil && old.Campaign != nil {
		// new is nil, old is not nil - set to null
		diff["campaign"] = nil
	} else if new.Campaign != nil && old.Campaign == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Campaign)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["campaign"] = gorm.Expr("? || ?", clause.Column{Name: "campaign"}, string(jsonValue))
		} else if err != nil {
			diff["campaign"] = new.Campaign
		}
	} else if new.Campaign != nil && old.Campaign != nil {
		// Both are not nil - use attribute-by-attribute diff
		CampaignDiff := new.Campaign.Diff(old.Campaign)
		if len(CampaignDiff) > 0 {
			jsonValue, err := sonic.Marshal(CampaignDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["campaign"] = gorm.Expr("? || ?", clause.Column{Name: "campaign"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["campaign"] = new.Campaign
			}
		}
	}

	return diff
}

// Diff compares this Account instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Account) Diff(old *Account) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare Settings

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Settings == nil && old.Settings != nil {
		// new is nil, old is not nil - set to null
		diff["Settings"] = nil
	} else if new.Settings != nil && old.Settings == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Settings)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Settings"] = gorm.Expr("? || ?", clause.Column{Name: "settings"}, string(jsonValue))
		} else if err != nil {
			diff["Settings"] = new.Settings
		}
	} else if new.Settings != nil && old.Settings != nil {
		// Both are not nil - use attribute-by-attribute diff
		SettingsDiff := new.Settings.Diff(old.Settings)
		if len(SettingsDiff) > 0 {
			jsonValue, err := sonic.Marshal(SettingsDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Settings"] = gorm.Expr("? || ?", clause.Column{Name: "settings"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Settings"] = new.Settings
			}
		}
	}

	// Compare Data

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// Use bytes.Equal for datatypes.JSON ([]byte underlying type)
	if !bytes.Equal([]byte(new.Data), []byte(old.Data)) {
		jsonValue, err := sonic.Marshal(new.Data)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Data"] = gorm.Expr("? || ?", clause.Column{Name: "data"}, string(jsonValue))
		} else if err != nil {
			// Fallback to regular assignment if JSON marshaling fails
			diff["Data"] = new.Data
		}
		// Skip adding to diff if JSON is empty (no-op update)
	}

	// Compare EncryptionKey

	// Simple type comparison
	if new.EncryptionKey != old.EncryptionKey {
		diff["EncryptionKey"] = new.EncryptionKey
	}

	// Compare IsActive

	// Simple type comparison
	if new.IsActive != old.IsActive {
		diff["IsActive"] = new.IsActive
	}

	// Compare IsCampaignActive

	// Simple type comparison
	if new.IsCampaignActive != old.IsCampaignActive {
		diff["IsCampaignActive"] = new.IsCampaignActive
	}

	// Compare WizardProgress

	// Simple type comparison
	if new.WizardProgress != old.WizardProgress {
		diff["WizardProgress"] = new.WizardProgress
	}

	// Compare Plan

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Plan == nil && old.Plan != nil {
		// new is nil, old is not nil - set to null
		diff["Plan"] = nil
	} else if new.Plan != nil && old.Plan == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Plan)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Plan"] = gorm.Expr("? || ?", clause.Column{Name: "plan"}, string(jsonValue))
		} else if err != nil {
			diff["Plan"] = new.Plan
		}
	} else if new.Plan != nil && old.Plan != nil {
		// Both are not nil - use attribute-by-attribute diff
		PlanDiff := new.Plan.Diff(old.Plan)
		if len(PlanDiff) > 0 {
			jsonValue, err := sonic.Marshal(PlanDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Plan"] = gorm.Expr("? || ?", clause.Column{Name: "plan"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Plan"] = new.Plan
			}
		}
	}

	// Compare AgnusSignatureKey

	// Simple type comparison
	if new.AgnusSignatureKey != old.AgnusSignatureKey {
		diff["AgnusSignatureKey"] = new.AgnusSignatureKey
	}

	// Compare ExpiresAt

	// Time comparison

	// Pointer to time comparison
	if (new.ExpiresAt == nil) != (old.ExpiresAt == nil) || (new.ExpiresAt != nil && !new.ExpiresAt.Equal(*old.ExpiresAt)) {
		diff["ExpiresAt"] = new.ExpiresAt
	}

	// Compare CorrelationId

	// Simple type comparison
	if new.CorrelationId != old.CorrelationId {
		diff["CorrelationId"] = new.CorrelationId
	}

	// Compare Branch

	// Simple type comparison
	if new.Branch != old.Branch {
		diff["Branch"] = new.Branch
	}

	// Compare DefaultDepartmentId

	// UUID comparison

	// Direct UUID comparison
	if new.DefaultDepartmentId != old.DefaultDepartmentId {
		diff["DefaultDepartmentId"] = new.DefaultDepartmentId
	}

	// Compare Users

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Users, old.Users) {
		diff["Users"] = new.Users
	}

	// Compare Messages

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Messages, old.Messages) {
		diff["Messages"] = new.Messages
	}

	// Compare Services

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Services, old.Services) {
		diff["Services"] = new.Services
	}

	// Compare ClusterId

	// UUID comparison

	// Direct UUID comparison
	if new.ClusterId != old.ClusterId {
		diff["ClusterId"] = new.ClusterId
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this Answer instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Answer) Diff(old *Answer) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["Text"] = new.Text
	}

	// Compare Reason

	// Simple type comparison
	if new.Reason != old.Reason {
		diff["Reason"] = new.Reason
	}

	// Compare TicketId

	// UUID comparison

	// Direct UUID comparison
	if new.TicketId != old.TicketId {
		diff["TicketId"] = new.TicketId
	}

	// Compare Ticket

	// Comparable type comparison
	if new.Ticket != old.Ticket {
		diff["Ticket"] = new.Ticket
	}

	// Compare QuestionId

	// UUID comparison

	// Direct UUID comparison
	if new.QuestionId != old.QuestionId {
		diff["QuestionId"] = new.QuestionId
	}

	// Compare Question

	// Comparable type comparison
	if new.Question != old.Question {
		diff["Question"] = new.Question
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this CampaignConfig instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *CampaignConfig) Diff(old *CampaignConfig) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare MinInterval

	// Simple type comparison
	if new.MinInterval != old.MinInterval {
		diff["minInterval"] = new.MinInterval
	}

	// Compare MaxInterval

	// Simple type comparison
	if new.MaxInterval != old.MaxInterval {
		diff["maxInterval"] = new.MaxInterval
	}

	// Compare NameField

	// Simple type comparison
	if new.NameField != old.NameField {
		diff["nameField"] = new.NameField
	}

	// Compare NumberField

	// Simple type comparison
	if new.NumberField != old.NumberField {
		diff["numberField"] = new.NumberField
	}

	// Compare Delimiter

	// Simple type comparison
	if new.Delimiter != old.Delimiter {
		diff["delimiter"] = new.Delimiter
	}

	// Compare Error

	// Comparable type comparison
	if new.Error != old.Error {
		diff["error"] = new.Error
	}

	return diff
}

// Diff compares this Campaign instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Campaign) Diff(old *Campaign) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Title

	// Simple type comparison
	if new.Title != old.Title {
		diff["Title"] = new.Title
	}

	// Compare Status

	// Simple type comparison
	if new.Status != old.Status {
		diff["Status"] = new.Status
	}

	// Compare Config

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Config == nil && old.Config != nil {
		// new is nil, old is not nil - set to null
		diff["Config"] = nil
	} else if new.Config != nil && old.Config == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Config)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Config"] = gorm.Expr("? || ?", clause.Column{Name: "config"}, string(jsonValue))
		} else if err != nil {
			diff["Config"] = new.Config
		}
	} else if new.Config != nil && old.Config != nil {
		// Both are not nil - use attribute-by-attribute diff
		ConfigDiff := new.Config.Diff(old.Config)
		if len(ConfigDiff) > 0 {
			jsonValue, err := sonic.Marshal(ConfigDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Config"] = gorm.Expr("? || ?", clause.Column{Name: "config"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Config"] = new.Config
			}
		}
	}

	// Compare IsScheduled

	// Simple type comparison
	if new.IsScheduled != old.IsScheduled {
		diff["IsScheduled"] = new.IsScheduled
	}

	// Compare SendsAt

	// Time comparison

	// Pointer to time comparison
	if (new.SendsAt == nil) != (old.SendsAt == nil) || (new.SendsAt != nil && !new.SendsAt.Equal(*old.SendsAt)) {
		diff["SendsAt"] = new.SendsAt
	}

	// Compare StartedAt

	// Time comparison

	// Pointer to time comparison
	if (new.StartedAt == nil) != (old.StartedAt == nil) || (new.StartedAt != nil && !new.StartedAt.Equal(*old.StartedAt)) {
		diff["StartedAt"] = new.StartedAt
	}

	// Compare FinishedAt

	// Time comparison

	// Pointer to time comparison
	if (new.FinishedAt == nil) != (old.FinishedAt == nil) || (new.FinishedAt != nil && !new.FinishedAt.Equal(*old.FinishedAt)) {
		diff["FinishedAt"] = new.FinishedAt
	}

	// Compare TotalMessagesCount

	// Simple type comparison
	if new.TotalMessagesCount != old.TotalMessagesCount {
		diff["TotalMessagesCount"] = new.TotalMessagesCount
	}

	// Compare SentMessagesCount

	// Simple type comparison
	if new.SentMessagesCount != old.SentMessagesCount {
		diff["SentMessagesCount"] = new.SentMessagesCount
	}

	// Compare TotalContacts

	// Simple type comparison
	if new.TotalContacts != old.TotalContacts {
		diff["TotalContacts"] = new.TotalContacts
	}

	// Compare TotalContactsImported

	// Simple type comparison
	if new.TotalContactsImported != old.TotalContactsImported {
		diff["TotalContactsImported"] = new.TotalContactsImported
	}

	// Compare TotalValidContacts

	// Simple type comparison
	if new.TotalValidContacts != old.TotalValidContacts {
		diff["TotalValidContacts"] = new.TotalValidContacts
	}

	// Compare MustOpenTicket

	// Simple type comparison
	if new.MustOpenTicket != old.MustOpenTicket {
		diff["MustOpenTicket"] = new.MustOpenTicket
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare ServiceId

	// UUID comparison

	// Direct UUID comparison
	if new.ServiceId != old.ServiceId {
		diff["ServiceId"] = new.ServiceId
	}

	// Compare DefaultDepartmentId

	// UUID comparison

	// Direct UUID comparison
	if new.DefaultDepartmentId != old.DefaultDepartmentId {
		diff["DefaultDepartmentId"] = new.DefaultDepartmentId
	}

	// Compare DefaultUserId

	// UUID comparison

	// Direct UUID comparison
	if new.DefaultUserId != old.DefaultUserId {
		diff["DefaultUserId"] = new.DefaultUserId
	}

	// Compare CreatedById

	// UUID comparison

	// Direct UUID comparison
	if new.CreatedById != old.CreatedById {
		diff["CreatedById"] = new.CreatedById
	}

	// Compare SentById

	// UUID comparison

	// Direct UUID comparison
	if new.SentById != old.SentById {
		diff["SentById"] = new.SentById
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Service

	// Comparable type comparison
	if new.Service != old.Service {
		diff["Service"] = new.Service
	}

	// Compare DefaultDepartment

	// Comparable type comparison
	if new.DefaultDepartment != old.DefaultDepartment {
		diff["DefaultDepartment"] = new.DefaultDepartment
	}

	// Compare DefaultUser

	// Comparable type comparison
	if new.DefaultUser != old.DefaultUser {
		diff["DefaultUser"] = new.DefaultUser
	}

	// Compare CreatedBy

	// Comparable type comparison
	if new.CreatedBy != old.CreatedBy {
		diff["CreatedBy"] = new.CreatedBy
	}

	// Compare SentBy

	// Comparable type comparison
	if new.SentBy != old.SentBy {
		diff["SentBy"] = new.SentBy
	}

	// Compare Tags

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Tags, old.Tags) {
		diff["Tags"] = new.Tags
	}

	// Compare Messages

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Messages, old.Messages) {
		diff["Messages"] = new.Messages
	}

	// Compare Progress

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Progress, old.Progress) {
		diff["Progress"] = new.Progress
	}

	// Compare ImportFile

	// Comparable type comparison
	if new.ImportFile != old.ImportFile {
		diff["ImportFile"] = new.ImportFile
	}

	return diff
}

// Diff compares this ExtraOptions instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ExtraOptions) Diff(old *ExtraOptions) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Hsm

	// Comparable type comparison
	if new.Hsm != old.Hsm {
		diff["hsm"] = new.Hsm
	}

	// Compare Parameters

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Parameters, old.Parameters) {
		diff["parameters"] = new.Parameters
	}

	// Compare FileTemplate

	// Comparable type comparison
	if new.FileTemplate != old.FileTemplate {
		diff["fileTemplate"] = new.FileTemplate
	}

	return diff
}

// Diff compares this CampaignMessage instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *CampaignMessage) Diff(old *CampaignMessage) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["Text"] = new.Text
	}

	// Compare ExtraOptions

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.ExtraOptions == nil && old.ExtraOptions != nil {
		// new is nil, old is not nil - set to null
		diff["ExtraOptions"] = nil
	} else if new.ExtraOptions != nil && old.ExtraOptions == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.ExtraOptions)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["ExtraOptions"] = gorm.Expr("? || ?", clause.Column{Name: "extra_options"}, string(jsonValue))
		} else if err != nil {
			diff["ExtraOptions"] = new.ExtraOptions
		}
	} else if new.ExtraOptions != nil && old.ExtraOptions != nil {
		// Both are not nil - use attribute-by-attribute diff
		ExtraOptionsDiff := new.ExtraOptions.Diff(old.ExtraOptions)
		if len(ExtraOptionsDiff) > 0 {
			jsonValue, err := sonic.Marshal(ExtraOptionsDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["ExtraOptions"] = gorm.Expr("? || ?", clause.Column{Name: "extra_options"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["ExtraOptions"] = new.ExtraOptions
			}
		}
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare UserId

	// UUID comparison

	// Direct UUID comparison
	if new.UserId != old.UserId {
		diff["UserId"] = new.UserId
	}

	// Compare CampaignId

	// UUID comparison

	// Direct UUID comparison
	if new.CampaignId != old.CampaignId {
		diff["CampaignId"] = new.CampaignId
	}

	// Compare HsmId

	// UUID comparison

	// Direct UUID comparison
	if new.HsmId != old.HsmId {
		diff["HsmId"] = new.HsmId
	}

	// Compare HsmFileId

	// UUID comparison

	// Direct UUID comparison
	if new.HsmFileId != old.HsmFileId {
		diff["HsmFileId"] = new.HsmFileId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare User

	// Comparable type comparison
	if new.User != old.User {
		diff["User"] = new.User
	}

	// Compare Campaign

	// Comparable type comparison
	if new.Campaign != old.Campaign {
		diff["Campaign"] = new.Campaign
	}

	// Compare File

	// Comparable type comparison
	if new.File != old.File {
		diff["File"] = new.File
	}

	// Compare HsmFile

	// Comparable type comparison
	if new.HsmFile != old.HsmFile {
		diff["HsmFile"] = new.HsmFile
	}

	// Compare Hsm

	// Comparable type comparison
	if new.Hsm != old.Hsm {
		diff["Hsm"] = new.Hsm
	}

	// Compare Progress

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Progress, old.Progress) {
		diff["Progress"] = new.Progress
	}

	return diff
}

// Diff compares this CampaignMessageProgress instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *CampaignMessageProgress) Diff(old *CampaignMessageProgress) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare SentAt

	// Time comparison

	// Pointer to time comparison
	if (new.SentAt == nil) != (old.SentAt == nil) || (new.SentAt != nil && !new.SentAt.Equal(*old.SentAt)) {
		diff["SentAt"] = new.SentAt
	}

	// Compare Failed

	// Simple type comparison
	if new.Failed != old.Failed {
		diff["Failed"] = new.Failed
	}

	// Compare FailReason

	// Simple type comparison
	if new.FailReason != old.FailReason {
		diff["FailReason"] = new.FailReason
	}

	// Compare Parameters

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// Use bytes.Equal for datatypes.JSON ([]byte underlying type)
	if !bytes.Equal([]byte(new.Parameters), []byte(old.Parameters)) {
		jsonValue, err := sonic.Marshal(new.Parameters)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Parameters"] = gorm.Expr("? || ?", clause.Column{Name: "parameters"}, string(jsonValue))
		} else if err != nil {
			// Fallback to regular assignment if JSON marshaling fails
			diff["Parameters"] = new.Parameters
		}
		// Skip adding to diff if JSON is empty (no-op update)
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare CampaignId

	// UUID comparison

	// Direct UUID comparison
	if new.CampaignId != old.CampaignId {
		diff["CampaignId"] = new.CampaignId
	}

	// Compare ContactId

	// UUID comparison

	// Direct UUID comparison
	if new.ContactId != old.ContactId {
		diff["ContactId"] = new.ContactId
	}

	// Compare ServiceId

	// UUID comparison

	// Direct UUID comparison
	if new.ServiceId != old.ServiceId {
		diff["ServiceId"] = new.ServiceId
	}

	// Compare MessageId

	// UUID comparison

	// Direct UUID comparison
	if new.MessageId != old.MessageId {
		diff["MessageId"] = new.MessageId
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare CampaignMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.CampaignMessageId != old.CampaignMessageId {
		diff["CampaignMessageId"] = new.CampaignMessageId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Contact

	// Comparable type comparison
	if new.Contact != old.Contact {
		diff["Contact"] = new.Contact
	}

	// Compare Campaign

	// Comparable type comparison
	if new.Campaign != old.Campaign {
		diff["Campaign"] = new.Campaign
	}

	// Compare CampaignMessage

	// Comparable type comparison
	if new.CampaignMessage != old.CampaignMessage {
		diff["CampaignMessage"] = new.CampaignMessage
	}

	// Compare Message

	// Comparable type comparison
	if new.Message != old.Message {
		diff["Message"] = new.Message
	}

	// Compare Service

	// Comparable type comparison
	if new.Service != old.Service {
		diff["Service"] = new.Service
	}

	return diff
}

// Diff compares this ContactDataSurvey instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ContactDataSurvey) Diff(old *ContactDataSurvey) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare ExpiresAt

	// Time comparison

	// Pointer to time comparison
	if (new.ExpiresAt == nil) != (old.ExpiresAt == nil) || (new.ExpiresAt != nil && !new.ExpiresAt.Equal(*old.ExpiresAt)) {
		diff["expiresAt"] = new.ExpiresAt
	}

	// Compare TicketId

	// UUID comparison

	// Direct UUID comparison
	if new.TicketId != old.TicketId {
		diff["ticketId"] = new.TicketId
	}

	// Compare QuestionId

	// UUID comparison

	// Direct UUID comparison
	if new.QuestionId != old.QuestionId {
		diff["questionId"] = new.QuestionId
	}

	// Compare Tries

	// Simple type comparison
	if new.Tries != old.Tries {
		diff["tries"] = new.Tries
	}

	return diff
}

// Diff compares this ContactDataBlock instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ContactDataBlock) Diff(old *ContactDataBlock) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Level

	// Simple type comparison
	if new.Level != old.Level {
		diff["level"] = new.Level
	}

	// Compare ByUserId

	// UUID comparison

	// Direct UUID comparison
	if new.ByUserId != old.ByUserId {
		diff["byUserId"] = new.ByUserId
	}

	// Compare Date

	// Time comparison

	// Direct time comparison
	if !new.Date.Equal(old.Date) {
		diff["date"] = new.Date

	}

	// Compare Description

	// Simple type comparison
	if new.Description != old.Description {
		diff["description"] = new.Description
	}

	return diff
}

// Diff compares this ContactData instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ContactData) Diff(old *ContactData) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare BotFinishedAt

	// Time comparison

	// Pointer to time comparison
	if (new.BotFinishedAt == nil) != (old.BotFinishedAt == nil) || (new.BotFinishedAt != nil && !new.BotFinishedAt.Equal(*old.BotFinishedAt)) {
		diff["botFinishedAt"] = new.BotFinishedAt
	}

	// Compare BotIsRunning

	// Simple type comparison
	if new.BotIsRunning != old.BotIsRunning {
		diff["botIsRunning"] = new.BotIsRunning
	}

	// Compare Number

	// Simple type comparison
	if new.Number != old.Number {
		diff["number"] = new.Number
	}

	// Compare Survey

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Survey == nil && old.Survey != nil {
		// new is nil, old is not nil - set to null
		diff["survey"] = nil
	} else if new.Survey != nil && old.Survey == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Survey)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["survey"] = gorm.Expr("? || ?", clause.Column{Name: "survey"}, string(jsonValue))
		} else if err != nil {
			diff["survey"] = new.Survey
		}
	} else if new.Survey != nil && old.Survey != nil {
		// Both are not nil - use attribute-by-attribute diff
		SurveyDiff := new.Survey.Diff(old.Survey)
		if len(SurveyDiff) > 0 {
			jsonValue, err := sonic.Marshal(SurveyDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["survey"] = gorm.Expr("? || ?", clause.Column{Name: "survey"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["survey"] = new.Survey
			}
		}
	}

	return diff
}

// Diff compares this Contact instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Contact) Diff(old *Contact) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare IdFromService

	// Simple type comparison
	if new.IdFromService != old.IdFromService {
		diff["IdFromService"] = new.IdFromService
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare InternalName

	// Simple type comparison
	if new.InternalName != old.InternalName {
		diff["InternalName"] = new.InternalName
	}

	// Compare AlternativeName

	// Simple type comparison
	if new.AlternativeName != old.AlternativeName {
		diff["AlternativeName"] = new.AlternativeName
	}

	// Compare IsGroup

	// Simple type comparison
	if new.IsGroup != old.IsGroup {
		diff["IsGroup"] = new.IsGroup
	}

	// Compare IsBroadcast

	// Simple type comparison
	if new.IsBroadcast != old.IsBroadcast {
		diff["IsBroadcast"] = new.IsBroadcast
	}

	// Compare IsMe

	// Simple type comparison
	if new.IsMe != old.IsMe {
		diff["IsMe"] = new.IsMe
	}

	// Compare IsMyContact

	// Simple type comparison
	if new.IsMyContact != old.IsMyContact {
		diff["IsMyContact"] = new.IsMyContact
	}

	// Compare HadChat

	// Simple type comparison
	if new.HadChat != old.HadChat {
		diff["HadChat"] = new.HadChat
	}

	// Compare Visible

	// Simple type comparison
	if new.Visible != old.Visible {
		diff["Visible"] = new.Visible
	}

	// Compare IsSilenced

	// Simple type comparison
	if new.IsSilenced != old.IsSilenced {
		diff["IsSilenced"] = new.IsSilenced
	}

	// Compare Data

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Data == nil && old.Data != nil {
		// new is nil, old is not nil - set to null
		diff["Data"] = nil
	} else if new.Data != nil && old.Data == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Data)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Data"] = gorm.Expr("? || ?", clause.Column{Name: "data"}, string(jsonValue))
		} else if err != nil {
			diff["Data"] = new.Data
		}
	} else if new.Data != nil && old.Data != nil {
		// Both are not nil - use attribute-by-attribute diff
		DataDiff := new.Data.Diff(old.Data)
		if len(DataDiff) > 0 {
			jsonValue, err := sonic.Marshal(DataDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Data"] = gorm.Expr("? || ?", clause.Column{Name: "data"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Data"] = new.Data
			}
		}
	}

	// Compare Note

	// Simple type comparison
	if new.Note != old.Note {
		diff["Note"] = new.Note
	}

	// Compare Unread

	// Simple type comparison
	if new.Unread != old.Unread {
		diff["Unread"] = new.Unread
	}

	// Compare LastMessageAt

	// Time comparison

	// Pointer to time comparison
	if (new.LastMessageAt == nil) != (old.LastMessageAt == nil) || (new.LastMessageAt != nil && !new.LastMessageAt.Equal(*old.LastMessageAt)) {
		diff["LastMessageAt"] = new.LastMessageAt
	}

	// Compare Unsubscribed

	// Simple type comparison
	if new.Unsubscribed != old.Unsubscribed {
		diff["Unsubscribed"] = new.Unsubscribed
	}

	// Compare LastContactMessageAt

	// Time comparison

	// Pointer to time comparison
	if (new.LastContactMessageAt == nil) != (old.LastContactMessageAt == nil) || (new.LastContactMessageAt != nil && !new.LastContactMessageAt.Equal(*old.LastContactMessageAt)) {
		diff["LastContactMessageAt"] = new.LastContactMessageAt
	}

	// Compare AcceptedTermAt

	// Time comparison

	// Pointer to time comparison
	if (new.AcceptedTermAt == nil) != (old.AcceptedTermAt == nil) || (new.AcceptedTermAt != nil && !new.AcceptedTermAt.Equal(*old.AcceptedTermAt)) {
		diff["AcceptedTermAt"] = new.AcceptedTermAt
	}

	// Compare HsmExpirationTime

	// Time comparison

	// Pointer to time comparison
	if (new.HsmExpirationTime == nil) != (old.HsmExpirationTime == nil) || (new.HsmExpirationTime != nil && !new.HsmExpirationTime.Equal(*old.HsmExpirationTime)) {
		diff["HsmExpirationTime"] = new.HsmExpirationTime
	}

	// Compare Block

	// Simple type comparison
	if new.Block != old.Block {
		diff["Block"] = new.Block
	}

	// Compare DataBlock

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.DataBlock == nil && old.DataBlock != nil {
		// new is nil, old is not nil - set to null
		diff["DataBlock"] = nil
	} else if new.DataBlock != nil && old.DataBlock == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.DataBlock)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["DataBlock"] = gorm.Expr("? || ?", clause.Column{Name: "data_block"}, string(jsonValue))
		} else if err != nil {
			diff["DataBlock"] = new.DataBlock
		}
	} else if new.DataBlock != nil && old.DataBlock != nil {
		// Both are not nil - use attribute-by-attribute diff
		DataBlockDiff := new.DataBlock.Diff(old.DataBlock)
		if len(DataBlockDiff) > 0 {
			jsonValue, err := sonic.Marshal(DataBlockDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["DataBlock"] = gorm.Expr("? || ?", clause.Column{Name: "data_block"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["DataBlock"] = new.DataBlock
			}
		}
	}

	// Compare Origin

	// Simple type comparison
	if new.Origin != old.Origin {
		diff["Origin"] = new.Origin
	}

	// Compare Status

	// Simple type comparison
	if new.Status != old.Status {
		diff["Status"] = new.Status
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare ServiceId

	// UUID comparison

	// Direct UUID comparison
	if new.ServiceId != old.ServiceId {
		diff["ServiceId"] = new.ServiceId
	}

	// Compare DefaultDepartmentId

	// UUID comparison

	// Direct UUID comparison
	if new.DefaultDepartmentId != old.DefaultDepartmentId {
		diff["DefaultDepartmentId"] = new.DefaultDepartmentId
	}

	// Compare DefaultDepartment

	// Comparable type comparison
	if new.DefaultDepartment != old.DefaultDepartment {
		diff["DefaultDepartment"] = new.DefaultDepartment
	}

	// Compare DefaultUserId

	// UUID comparison

	// Direct UUID comparison
	if new.DefaultUserId != old.DefaultUserId {
		diff["DefaultUserId"] = new.DefaultUserId
	}

	// Compare DefaultUser

	// Comparable type comparison
	if new.DefaultUser != old.DefaultUser {
		diff["DefaultUser"] = new.DefaultUser
	}

	// Compare CurrentTicketId

	// UUID comparison

	// Direct UUID comparison
	if new.CurrentTicketId != old.CurrentTicketId {
		diff["CurrentTicketId"] = new.CurrentTicketId
	}

	// Compare LastMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.LastMessageId != old.LastMessageId {
		diff["LastMessageId"] = new.LastMessageId
	}

	// Compare PersonId

	// UUID comparison

	// Direct UUID comparison
	if new.PersonId != old.PersonId {
		diff["PersonId"] = new.PersonId
	}

	// Compare ContactBlockListControlId

	// UUID comparison

	// Direct UUID comparison
	if new.ContactBlockListControlId != old.ContactBlockListControlId {
		diff["ContactBlockListControlId"] = new.ContactBlockListControlId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Service

	// Comparable type comparison
	if new.Service != old.Service {
		diff["Service"] = new.Service
	}

	// Compare LastMessage

	// Comparable type comparison
	if new.LastMessage != old.LastMessage {
		diff["LastMessage"] = new.LastMessage
	}

	// Compare Participants

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Participants, old.Participants) {
		diff["Participants"] = new.Participants
	}

	// Compare Groups

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Groups, old.Groups) {
		diff["Groups"] = new.Groups
	}

	// Compare Tags

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Tags, old.Tags) {
		diff["Tags"] = new.Tags
	}

	// Compare Avatar

	// Comparable type comparison
	if new.Avatar != old.Avatar {
		diff["Avatar"] = new.Avatar
	}

	// Compare ThumbAvatar

	// Comparable type comparison
	if new.ThumbAvatar != old.ThumbAvatar {
		diff["ThumbAvatar"] = new.ThumbAvatar
	}

	// Compare CurrentTicket

	// Comparable type comparison
	if new.CurrentTicket != old.CurrentTicket {
		diff["CurrentTicket"] = new.CurrentTicket
	}

	// Compare Tickets

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Tickets, old.Tickets) {
		diff["Tickets"] = new.Tickets
	}

	// Compare User

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.User, old.User) {
		diff["User"] = new.User
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	// Compare ArchivedAt

	// Time comparison

	// Pointer to time comparison
	if (new.ArchivedAt == nil) != (old.ArchivedAt == nil) || (new.ArchivedAt != nil && !new.ArchivedAt.Equal(*old.ArchivedAt)) {
		diff["ArchivedAt"] = new.ArchivedAt
	}

	// Compare Person

	// Comparable type comparison
	if new.Person != old.Person {
		diff["Person"] = new.Person
	}

	return diff
}

// Diff compares this ContactBlockList instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ContactBlockList) Diff(old *ContactBlockList) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare Status

	// Simple type comparison
	if new.Status != old.Status {
		diff["Status"] = new.Status
	}

	// Compare DefaultDDI

	// Comparable type comparison
	if new.DefaultDDI != old.DefaultDDI {
		diff["DefaultDDI"] = new.DefaultDDI
	}

	// Compare SaveCount

	// Comparable type comparison
	if new.SaveCount != old.SaveCount {
		diff["SaveCount"] = new.SaveCount
	}

	// Compare ValidCount

	// Comparable type comparison
	if new.ValidCount != old.ValidCount {
		diff["ValidCount"] = new.ValidCount
	}

	// Compare TotalCount

	// Comparable type comparison
	if new.TotalCount != old.TotalCount {
		diff["TotalCount"] = new.TotalCount
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare UserId

	// UUID comparison

	// Direct UUID comparison
	if new.UserId != old.UserId {
		diff["UserId"] = new.UserId
	}

	// Compare User

	// Comparable type comparison
	if new.User != old.User {
		diff["User"] = new.User
	}

	// Compare CreatedAt

	// Time comparison

	// Direct time comparison
	if !new.CreatedAt.Equal(old.CreatedAt) {
		diff["CreatedAt"] = new.CreatedAt

	}

	// Compare UpdatedAt

	// Time comparison

	// Direct time comparison
	if !new.UpdatedAt.Equal(old.UpdatedAt) {
		diff["UpdatedAt"] = new.UpdatedAt

	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this ContactBlockListControl instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ContactBlockListControl) Diff(old *ContactBlockListControl) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Reason

	// Simple type comparison
	if new.Reason != old.Reason {
		diff["Reason"] = new.Reason
	}

	// Compare Status

	// Simple type comparison
	if new.Status != old.Status {
		diff["Status"] = new.Status
	}

	// Compare Action

	// Simple type comparison
	if new.Action != old.Action {
		diff["Action"] = new.Action
	}

	// Compare RevertExceptions

	// Simple type comparison
	if new.RevertExceptions != old.RevertExceptions {
		diff["RevertExceptions"] = new.RevertExceptions
	}

	// Compare UpdatedCount

	// Comparable type comparison
	if new.UpdatedCount != old.UpdatedCount {
		diff["UpdatedCount"] = new.UpdatedCount
	}

	// Compare ProcessCount

	// Comparable type comparison
	if new.ProcessCount != old.ProcessCount {
		diff["ProcessCount"] = new.ProcessCount
	}

	// Compare TotalCount

	// Comparable type comparison
	if new.TotalCount != old.TotalCount {
		diff["TotalCount"] = new.TotalCount
	}

	// Compare ContactBlockListId

	// UUID comparison

	// Direct UUID comparison
	if new.ContactBlockListId != old.ContactBlockListId {
		diff["ContactBlockListId"] = new.ContactBlockListId
	}

	// Compare ContactBlockList

	// Comparable type comparison
	if new.ContactBlockList != old.ContactBlockList {
		diff["ContactBlockList"] = new.ContactBlockList
	}

	// Compare ServiceId

	// UUID comparison

	// Direct UUID comparison
	if new.ServiceId != old.ServiceId {
		diff["ServiceId"] = new.ServiceId
	}

	// Compare Service

	// Comparable type comparison
	if new.Service != old.Service {
		diff["Service"] = new.Service
	}

	// Compare UserId

	// UUID comparison

	// Direct UUID comparison
	if new.UserId != old.UserId {
		diff["UserId"] = new.UserId
	}

	// Compare User

	// Comparable type comparison
	if new.User != old.User {
		diff["User"] = new.User
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare CreatedAt

	// Time comparison

	// Direct time comparison
	if !new.CreatedAt.Equal(old.CreatedAt) {
		diff["CreatedAt"] = new.CreatedAt

	}

	// Compare UpdatedAt

	// Time comparison

	// Direct time comparison
	if !new.UpdatedAt.Equal(old.UpdatedAt) {
		diff["UpdatedAt"] = new.UpdatedAt

	}

	// Compare DeletedAt

	// Time comparison

	// Pointer to time comparison
	if (new.DeletedAt == nil) != (old.DeletedAt == nil) || (new.DeletedAt != nil && !new.DeletedAt.Equal(*old.DeletedAt)) {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this ContactBlockListItem instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ContactBlockListItem) Diff(old *ContactBlockListItem) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare IdFromService

	// Simple type comparison
	if new.IdFromService != old.IdFromService {
		diff["IdFromService"] = new.IdFromService
	}

	// Compare ContactBlockListId

	// UUID comparison

	// Direct UUID comparison
	if new.ContactBlockListId != old.ContactBlockListId {
		diff["ContactBlockListId"] = new.ContactBlockListId
	}

	// Compare ContactBlockList

	// Comparable type comparison
	if new.ContactBlockList != old.ContactBlockList {
		diff["ContactBlockList"] = new.ContactBlockList
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare CreatedAt

	// Time comparison

	// Direct time comparison
	if !new.CreatedAt.Equal(old.CreatedAt) {
		diff["CreatedAt"] = new.CreatedAt

	}

	// Compare UpdatedAt

	// Time comparison

	// Direct time comparison
	if !new.UpdatedAt.Equal(old.UpdatedAt) {
		diff["UpdatedAt"] = new.UpdatedAt

	}

	return diff
}

// Diff compares this Department instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Department) Diff(old *Department) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare DistributionId

	// Simple type comparison
	if new.DistributionId != old.DistributionId {
		diff["DistributionId"] = new.DistributionId
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare ArchivedAt

	// Time comparison

	// Pointer to time comparison
	if (new.ArchivedAt == nil) != (old.ArchivedAt == nil) || (new.ArchivedAt != nil && !new.ArchivedAt.Equal(*old.ArchivedAt)) {
		diff["ArchivedAt"] = new.ArchivedAt
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Tickets

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Tickets, old.Tickets) {
		diff["Tickets"] = new.Tickets
	}

	// Compare Users

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Users, old.Users) {
		diff["Users"] = new.Users
	}

	return diff
}

// Diff compares this File instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *File) Diff(old *File) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare Checksum

	// Simple type comparison
	if new.Checksum != old.Checksum {
		diff["Checksum"] = new.Checksum
	}

	// Compare Mimetype

	// Simple type comparison
	if new.Mimetype != old.Mimetype {
		diff["Mimetype"] = new.Mimetype
	}

	// Compare Extension

	// Simple type comparison
	if new.Extension != old.Extension {
		diff["Extension"] = new.Extension
	}

	// Compare AttachedId

	// UUID comparison

	// Direct UUID comparison
	if new.AttachedId != old.AttachedId {
		diff["AttachedId"] = new.AttachedId
	}

	// Compare AttachedType

	// Simple type comparison
	if new.AttachedType != old.AttachedType {
		diff["AttachedType"] = new.AttachedType
	}

	// Compare Storage

	// Simple type comparison
	if new.Storage != old.Storage {
		diff["Storage"] = new.Storage
	}

	// Compare IsEncrypted

	// Simple type comparison
	if new.IsEncrypted != old.IsEncrypted {
		diff["IsEncrypted"] = new.IsEncrypted
	}

	// Compare IV

	// Simple type comparison
	if new.IV != old.IV {
		diff["IV"] = new.IV
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare Filename

	// Simple type comparison
	if new.Filename != old.Filename {
		diff["Filename"] = new.Filename
	}

	// Compare PublicFilename

	// Simple type comparison
	if new.PublicFilename != old.PublicFilename {
		diff["PublicFilename"] = new.PublicFilename
	}

	// Compare Filepath

	// Simple type comparison
	if new.Filepath != old.Filepath {
		diff["Filepath"] = new.Filepath
	}

	return diff
}

// Diff compares this Link instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Link) Diff(old *Link) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare MessageId

	// UUID comparison

	// Direct UUID comparison
	if new.MessageId != old.MessageId {
		diff["MessageId"] = new.MessageId
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare Message

	// Comparable type comparison
	if new.Message != old.Message {
		diff["Message"] = new.Message
	}

	return diff
}

// Diff compares this MessageError instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *MessageError) Diff(old *MessageError) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Code

	// Simple type comparison
	if new.Code != old.Code {
		diff["code"] = new.Code
	}

	// Compare Error

	// Simple type comparison
	if new.Error != old.Error {
		diff["error"] = new.Error
	}

	// Compare Message

	// Simple type comparison
	if new.Message != old.Message {
		diff["message"] = new.Message
	}

	// Compare OriginalError

	// Simple type comparison
	if new.OriginalError != old.OriginalError {
		diff["originalError"] = new.OriginalError
	}

	return diff
}

// Diff compares this FileDownload instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *FileDownload) Diff(old *FileDownload) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare StartedAt

	// Time comparison

	// Pointer to time comparison
	if (new.StartedAt == nil) != (old.StartedAt == nil) || (new.StartedAt != nil && !new.StartedAt.Equal(*old.StartedAt)) {
		diff["startedAt"] = new.StartedAt
	}

	// Compare EndedAt

	// Time comparison

	// Pointer to time comparison
	if (new.EndedAt == nil) != (old.EndedAt == nil) || (new.EndedAt != nil && !new.EndedAt.Equal(*old.EndedAt)) {
		diff["endedAt"] = new.EndedAt
	}

	// Compare IsDownloading

	// Simple type comparison
	if new.IsDownloading != old.IsDownloading {
		diff["isDownloading"] = new.IsDownloading
	}

	return diff
}

// Diff compares this Location instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Location) Diff(old *Location) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Lat

	// Simple type comparison
	if new.Lat != old.Lat {
		diff["lat"] = new.Lat
	}

	// Compare Lng

	// Simple type comparison
	if new.Lng != old.Lng {
		diff["lng"] = new.Lng
	}

	// Compare PreviewUrl

	// Simple type comparison
	if new.PreviewUrl != old.PreviewUrl {
		diff["previewUrl"] = new.PreviewUrl
	}

	return diff
}

// Diff compares this CtwaContext instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *CtwaContext) Diff(old *CtwaContext) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare ConversionSource

	// Simple type comparison
	if new.ConversionSource != old.ConversionSource {
		diff["conversionSource"] = new.ConversionSource
	}

	// Compare Description

	// Simple type comparison
	if new.Description != old.Description {
		diff["description"] = new.Description
	}

	// Compare IsSuspiciousLink

	// Simple type comparison
	if new.IsSuspiciousLink != old.IsSuspiciousLink {
		diff["isSuspiciousLink"] = new.IsSuspiciousLink
	}

	// Compare MediaType

	// Simple type comparison
	if new.MediaType != old.MediaType {
		diff["mediaType"] = new.MediaType
	}

	// Compare MediaUrl

	// Simple type comparison
	if new.MediaUrl != old.MediaUrl {
		diff["mediaUrl"] = new.MediaUrl
	}

	// Compare SourceUrl

	// Simple type comparison
	if new.SourceUrl != old.SourceUrl {
		diff["sourceUrl"] = new.SourceUrl
	}

	// Compare ThumbnailUrl

	// Simple type comparison
	if new.ThumbnailUrl != old.ThumbnailUrl {
		diff["thumbnailUrl"] = new.ThumbnailUrl
	}

	// Compare Title

	// Simple type comparison
	if new.Title != old.Title {
		diff["title"] = new.Title
	}

	return diff
}

// Diff compares this MessageHsmParameterImage instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *MessageHsmParameterImage) Diff(old *MessageHsmParameterImage) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Link

	// Simple type comparison
	if new.Link != old.Link {
		diff["link"] = new.Link
	}

	return diff
}

// Diff compares this MessageHsmParameters instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *MessageHsmParameters) Diff(old *MessageHsmParameters) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["type"] = new.Type
	}

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["text"] = new.Text
	}

	// Compare Image

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Image == nil && old.Image != nil {
		// new is nil, old is not nil - set to null
		diff["image"] = nil
	} else if new.Image != nil && old.Image == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Image)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["image"] = gorm.Expr("? || ?", clause.Column{Name: "image"}, string(jsonValue))
		} else if err != nil {
			diff["image"] = new.Image
		}
	} else if new.Image != nil && old.Image != nil {
		// Both are not nil - use attribute-by-attribute diff
		ImageDiff := new.Image.Diff(old.Image)
		if len(ImageDiff) > 0 {
			jsonValue, err := sonic.Marshal(ImageDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["image"] = gorm.Expr("? || ?", clause.Column{Name: "image"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["image"] = new.Image
			}
		}
	}

	return diff
}

// Diff compares this HsmParameters instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *HsmParameters) Diff(old *HsmParameters) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["type"] = new.Type
	}

	// Compare Parameters

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Parameters, old.Parameters) {
		diff["parameters"] = new.Parameters
	}

	return diff
}

// Diff compares this InteractiveMessage instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveMessage) Diff(old *InteractiveMessage) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["type"] = new.Type
	}

	// Compare Header

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Header == nil && old.Header != nil {
		// new is nil, old is not nil - set to null
		diff["header"] = nil
	} else if new.Header != nil && old.Header == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Header)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["header"] = gorm.Expr("? || ?", clause.Column{Name: "header"}, string(jsonValue))
		} else if err != nil {
			diff["header"] = new.Header
		}
	} else if new.Header != nil && old.Header != nil {
		// Both are not nil - use attribute-by-attribute diff
		HeaderDiff := new.Header.Diff(old.Header)
		if len(HeaderDiff) > 0 {
			jsonValue, err := sonic.Marshal(HeaderDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["header"] = gorm.Expr("? || ?", clause.Column{Name: "header"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["header"] = new.Header
			}
		}
	}

	// Compare Body

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Body == nil && old.Body != nil {
		// new is nil, old is not nil - set to null
		diff["body"] = nil
	} else if new.Body != nil && old.Body == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Body)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["body"] = gorm.Expr("? || ?", clause.Column{Name: "body"}, string(jsonValue))
		} else if err != nil {
			diff["body"] = new.Body
		}
	} else if new.Body != nil && old.Body != nil {
		// Both are not nil - use attribute-by-attribute diff
		BodyDiff := new.Body.Diff(old.Body)
		if len(BodyDiff) > 0 {
			jsonValue, err := sonic.Marshal(BodyDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["body"] = gorm.Expr("? || ?", clause.Column{Name: "body"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["body"] = new.Body
			}
		}
	}

	// Compare Footer

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Footer == nil && old.Footer != nil {
		// new is nil, old is not nil - set to null
		diff["footer"] = nil
	} else if new.Footer != nil && old.Footer == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Footer)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["footer"] = gorm.Expr("? || ?", clause.Column{Name: "footer"}, string(jsonValue))
		} else if err != nil {
			diff["footer"] = new.Footer
		}
	} else if new.Footer != nil && old.Footer != nil {
		// Both are not nil - use attribute-by-attribute diff
		FooterDiff := new.Footer.Diff(old.Footer)
		if len(FooterDiff) > 0 {
			jsonValue, err := sonic.Marshal(FooterDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["footer"] = gorm.Expr("? || ?", clause.Column{Name: "footer"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["footer"] = new.Footer
			}
		}
	}

	// Compare Action

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Action == nil && old.Action != nil {
		// new is nil, old is not nil - set to null
		diff["action"] = nil
	} else if new.Action != nil && old.Action == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Action)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["action"] = gorm.Expr("? || ?", clause.Column{Name: "action"}, string(jsonValue))
		} else if err != nil {
			diff["action"] = new.Action
		}
	} else if new.Action != nil && old.Action != nil {
		// Both are not nil - use attribute-by-attribute diff
		ActionDiff := new.Action.Diff(old.Action)
		if len(ActionDiff) > 0 {
			jsonValue, err := sonic.Marshal(ActionDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["action"] = gorm.Expr("? || ?", clause.Column{Name: "action"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["action"] = new.Action
			}
		}
	}

	return diff
}

// Diff compares this InteractiveHeader instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveHeader) Diff(old *InteractiveHeader) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["type"] = new.Type
	}

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["text"] = new.Text
	}

	// Compare Image

	// Comparable type comparison
	if new.Image != old.Image {
		diff["image"] = new.Image
	}

	// Compare Video

	// Comparable type comparison
	if new.Video != old.Video {
		diff["video"] = new.Video
	}

	// Compare Document

	// Comparable type comparison
	if new.Document != old.Document {
		diff["document"] = new.Document
	}

	return diff
}

// Diff compares this InteractiveBody instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveBody) Diff(old *InteractiveBody) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["text"] = new.Text
	}

	return diff
}

// Diff compares this InteractiveFooter instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveFooter) Diff(old *InteractiveFooter) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["text"] = new.Text
	}

	return diff
}

// Diff compares this InteractiveAction instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveAction) Diff(old *InteractiveAction) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Button

	// Simple type comparison
	if new.Button != old.Button {
		diff["button"] = new.Button
	}

	// Compare Buttons

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Buttons, old.Buttons) {
		diff["buttons"] = new.Buttons
	}

	// Compare Sections

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Sections, old.Sections) {
		diff["sections"] = new.Sections
	}

	return diff
}

// Diff compares this InteractiveButton instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveButton) Diff(old *InteractiveButton) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["type"] = new.Type
	}

	// Compare Reply

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Reply == nil && old.Reply != nil {
		// new is nil, old is not nil - set to null
		diff["reply"] = nil
	} else if new.Reply != nil && old.Reply == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Reply)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["reply"] = gorm.Expr("? || ?", clause.Column{Name: "reply"}, string(jsonValue))
		} else if err != nil {
			diff["reply"] = new.Reply
		}
	} else if new.Reply != nil && old.Reply != nil {
		// Both are not nil - use attribute-by-attribute diff
		ReplyDiff := new.Reply.Diff(old.Reply)
		if len(ReplyDiff) > 0 {
			jsonValue, err := sonic.Marshal(ReplyDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["reply"] = gorm.Expr("? || ?", clause.Column{Name: "reply"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["reply"] = new.Reply
			}
		}
	}

	return diff
}

// Diff compares this InteractiveReplyButton instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveReplyButton) Diff(old *InteractiveReplyButton) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// Simple type comparison
	if new.Id != old.Id {
		diff["id"] = new.Id
	}

	// Compare Title

	// Simple type comparison
	if new.Title != old.Title {
		diff["title"] = new.Title
	}

	return diff
}

// Diff compares this InteractiveSection instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveSection) Diff(old *InteractiveSection) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Title

	// Simple type comparison
	if new.Title != old.Title {
		diff["title"] = new.Title
	}

	// Compare Rows

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Rows, old.Rows) {
		diff["rows"] = new.Rows
	}

	return diff
}

// Diff compares this InteractiveRow instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *InteractiveRow) Diff(old *InteractiveRow) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// Simple type comparison
	if new.Id != old.Id {
		diff["id"] = new.Id
	}

	// Compare Title

	// Simple type comparison
	if new.Title != old.Title {
		diff["title"] = new.Title
	}

	// Compare Description

	// Simple type comparison
	if new.Description != old.Description {
		diff["description"] = new.Description
	}

	return diff
}

// Diff compares this MessageData instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *MessageData) Diff(old *MessageData) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Ack

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Ack, old.Ack) {
		diff["ack"] = new.Ack
	}

	// Compare IsFirst

	// Simple type comparison
	if new.IsFirst != old.IsFirst {
		diff["isFirst"] = new.IsFirst
	}

	// Compare IsNew

	// Simple type comparison
	if new.IsNew != old.IsNew {
		diff["isNew"] = new.IsNew
	}

	// Compare HasReaction

	// Simple type comparison
	if new.HasReaction != old.HasReaction {
		diff["hasReaction"] = new.HasReaction
	}

	// Compare Error

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Error == nil && old.Error != nil {
		// new is nil, old is not nil - set to null
		diff["error"] = nil
	} else if new.Error != nil && old.Error == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Error)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["error"] = gorm.Expr("? || ?", clause.Column{Name: "error"}, string(jsonValue))
		} else if err != nil {
			diff["error"] = new.Error
		}
	} else if new.Error != nil && old.Error != nil {
		// Both are not nil - use attribute-by-attribute diff
		ErrorDiff := new.Error.Diff(old.Error)
		if len(ErrorDiff) > 0 {
			jsonValue, err := sonic.Marshal(ErrorDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["error"] = gorm.Expr("? || ?", clause.Column{Name: "error"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["error"] = new.Error
			}
		}
	}

	// Compare FileDownload

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.FileDownload == nil && old.FileDownload != nil {
		// new is nil, old is not nil - set to null
		diff["fileDownload"] = nil
	} else if new.FileDownload != nil && old.FileDownload == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.FileDownload)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["fileDownload"] = gorm.Expr("? || ?", clause.Column{Name: "file_download"}, string(jsonValue))
		} else if err != nil {
			diff["fileDownload"] = new.FileDownload
		}
	} else if new.FileDownload != nil && old.FileDownload != nil {
		// Both are not nil - use attribute-by-attribute diff
		FileDownloadDiff := new.FileDownload.Diff(old.FileDownload)
		if len(FileDownloadDiff) > 0 {
			jsonValue, err := sonic.Marshal(FileDownloadDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["fileDownload"] = gorm.Expr("? || ?", clause.Column{Name: "file_download"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["fileDownload"] = new.FileDownload
			}
		}
	}

	// Compare MediaId

	// Simple type comparison
	if new.MediaId != old.MediaId {
		diff["mediaId"] = new.MediaId
	}

	// Compare MediaUrl

	// Simple type comparison
	if new.MediaUrl != old.MediaUrl {
		diff["mediaUrl"] = new.MediaUrl
	}

	// Compare IsFromFirstSync

	// Simple type comparison
	if new.IsFromFirstSync != old.IsFromFirstSync {
		diff["isFromFirstSync"] = new.IsFromFirstSync
	}

	// Compare DontOpenTicket

	// Simple type comparison
	if new.DontOpenTicket != old.DontOpenTicket {
		diff["dontOpenTicket"] = new.DontOpenTicket
	}

	// Compare TicketOpen

	// Simple type comparison
	if new.TicketOpen != old.TicketOpen {
		diff["ticketOpen"] = new.TicketOpen
	}

	// Compare TicketTransfer

	// Simple type comparison
	if new.TicketTransfer != old.TicketTransfer {
		diff["ticketTransfer"] = new.TicketTransfer
	}

	// Compare TicketClose

	// Simple type comparison
	if new.TicketClose != old.TicketClose {
		diff["ticketClose"] = new.TicketClose
	}

	// Compare Location

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Location == nil && old.Location != nil {
		// new is nil, old is not nil - set to null
		diff["location"] = nil
	} else if new.Location != nil && old.Location == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Location)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["location"] = gorm.Expr("? || ?", clause.Column{Name: "location"}, string(jsonValue))
		} else if err != nil {
			diff["location"] = new.Location
		}
	} else if new.Location != nil && old.Location != nil {
		// Both are not nil - use attribute-by-attribute diff
		LocationDiff := new.Location.Diff(old.Location)
		if len(LocationDiff) > 0 {
			jsonValue, err := sonic.Marshal(LocationDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["location"] = gorm.Expr("? || ?", clause.Column{Name: "location"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["location"] = new.Location
			}
		}
	}

	// Compare Vcard

	// Simple type comparison
	if new.Vcard != old.Vcard {
		diff["vcard"] = new.Vcard
	}

	// Compare CtwaContext

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.CtwaContext == nil && old.CtwaContext != nil {
		// new is nil, old is not nil - set to null
		diff["ctwaContext"] = nil
	} else if new.CtwaContext != nil && old.CtwaContext == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.CtwaContext)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["ctwaContext"] = gorm.Expr("? || ?", clause.Column{Name: "ctwa_context"}, string(jsonValue))
		} else if err != nil {
			diff["ctwaContext"] = new.CtwaContext
		}
	} else if new.CtwaContext != nil && old.CtwaContext != nil {
		// Both are not nil - use attribute-by-attribute diff
		CtwaContextDiff := new.CtwaContext.Diff(old.CtwaContext)
		if len(CtwaContextDiff) > 0 {
			jsonValue, err := sonic.Marshal(CtwaContextDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["ctwaContext"] = gorm.Expr("? || ?", clause.Column{Name: "ctwa_context"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["ctwaContext"] = new.CtwaContext
			}
		}
	}

	// Compare Edited

	// Simple type comparison
	if new.Edited != old.Edited {
		diff["edited"] = new.Edited
	}

	// Compare HsmParameters

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.HsmParameters, old.HsmParameters) {
		diff["hsmParameters"] = new.HsmParameters
	}

	// Compare Interactive

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Interactive == nil && old.Interactive != nil {
		// new is nil, old is not nil - set to null
		diff["interactive"] = nil
	} else if new.Interactive != nil && old.Interactive == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Interactive)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["interactive"] = gorm.Expr("? || ?", clause.Column{Name: "interactive"}, string(jsonValue))
		} else if err != nil {
			diff["interactive"] = new.Interactive
		}
	} else if new.Interactive != nil && old.Interactive != nil {
		// Both are not nil - use attribute-by-attribute diff
		InteractiveDiff := new.Interactive.Diff(old.Interactive)
		if len(InteractiveDiff) > 0 {
			jsonValue, err := sonic.Marshal(InteractiveDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["interactive"] = gorm.Expr("? || ?", clause.Column{Name: "interactive"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["interactive"] = new.Interactive
			}
		}
	}

	return diff
}

// Diff compares this Message instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Message) Diff(old *Message) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare IdFromService

	// Simple type comparison
	if new.IdFromService != old.IdFromService {
		diff["IdFromService"] = new.IdFromService
	}

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["Text"] = new.Text
	}

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["Type"] = new.Type
	}

	// Compare Timestamp

	// Time comparison

	// Pointer to time comparison
	if (new.Timestamp == nil) != (old.Timestamp == nil) || (new.Timestamp != nil && !new.Timestamp.Equal(*old.Timestamp)) {
		diff["Timestamp"] = new.Timestamp
	}

	// Compare IsFromMe

	// Simple type comparison
	if new.IsFromMe != old.IsFromMe {
		diff["IsFromMe"] = new.IsFromMe
	}

	// Compare IsFromBot

	// Simple type comparison
	if new.IsFromBot != old.IsFromBot {
		diff["IsFromBot"] = new.IsFromBot
	}

	// Compare IsFromSync

	// Simple type comparison
	if new.IsFromSync != old.IsFromSync {
		diff["IsFromSync"] = new.IsFromSync
	}

	// Compare Sent

	// Simple type comparison
	if new.Sent != old.Sent {
		diff["Sent"] = new.Sent
	}

	// Compare Visible

	// Simple type comparison
	if new.Visible != old.Visible {
		diff["Visible"] = new.Visible
	}

	// Compare Data

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Data == nil && old.Data != nil {
		// new is nil, old is not nil - set to null
		diff["Data"] = nil
	} else if new.Data != nil && old.Data == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Data)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Data"] = gorm.Expr("? || ?", clause.Column{Name: "data"}, string(jsonValue))
		} else if err != nil {
			diff["Data"] = new.Data
		}
	} else if new.Data != nil && old.Data != nil {
		// Both are not nil - use attribute-by-attribute diff
		DataDiff := new.Data.Diff(old.Data)
		if len(DataDiff) > 0 {
			jsonValue, err := sonic.Marshal(DataDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Data"] = gorm.Expr("? || ?", clause.Column{Name: "data"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Data"] = new.Data
			}
		}
	}

	// Compare Origin

	// Simple type comparison
	if new.Origin != old.Origin {
		diff["Origin"] = new.Origin
	}

	// Compare HsmId

	// UUID comparison

	// Direct UUID comparison
	if new.HsmId != old.HsmId {
		diff["HsmId"] = new.HsmId
	}

	// Compare IsComment

	// Simple type comparison
	if new.IsComment != old.IsComment {
		diff["IsComment"] = new.IsComment
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare ServiceId

	// UUID comparison

	// Direct UUID comparison
	if new.ServiceId != old.ServiceId {
		diff["ServiceId"] = new.ServiceId
	}

	// Compare ContactId

	// UUID comparison

	// Direct UUID comparison
	if new.ContactId != old.ContactId {
		diff["ContactId"] = new.ContactId
	}

	// Compare FromId

	// UUID comparison

	// Direct UUID comparison
	if new.FromId != old.FromId {
		diff["FromId"] = new.FromId
	}

	// Compare ToId

	// UUID comparison

	// Direct UUID comparison
	if new.ToId != old.ToId {
		diff["ToId"] = new.ToId
	}

	// Compare UserId

	// UUID comparison

	// Direct UUID comparison
	if new.UserId != old.UserId {
		diff["UserId"] = new.UserId
	}

	// Compare TicketId

	// UUID comparison

	// Direct UUID comparison
	if new.TicketId != old.TicketId {
		diff["TicketId"] = new.TicketId
	}

	// Compare CampaignId

	// UUID comparison

	// Direct UUID comparison
	if new.CampaignId != old.CampaignId {
		diff["CampaignId"] = new.CampaignId
	}

	// Compare ReactionParentMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.ReactionParentMessageId != old.ReactionParentMessageId {
		diff["ReactionParentMessageId"] = new.ReactionParentMessageId
	}

	// Compare QuotedMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.QuotedMessageId != old.QuotedMessageId {
		diff["QuotedMessageId"] = new.QuotedMessageId
	}

	// Compare TicketUserId

	// UUID comparison

	// Direct UUID comparison
	if new.TicketUserId != old.TicketUserId {
		diff["TicketUserId"] = new.TicketUserId
	}

	// Compare TicketDepartmentId

	// UUID comparison

	// Direct UUID comparison
	if new.TicketDepartmentId != old.TicketDepartmentId {
		diff["TicketDepartmentId"] = new.TicketDepartmentId
	}

	// Compare BotId

	// UUID comparison

	// Direct UUID comparison
	if new.BotId != old.BotId {
		diff["BotId"] = new.BotId
	}

	// Compare HsmFileId

	// UUID comparison

	// Direct UUID comparison
	if new.HsmFileId != old.HsmFileId {
		diff["HsmFileId"] = new.HsmFileId
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Service

	// Comparable type comparison
	if new.Service != old.Service {
		diff["Service"] = new.Service
	}

	// Compare Contact

	// Comparable type comparison
	if new.Contact != old.Contact {
		diff["Contact"] = new.Contact
	}

	// Compare From

	// Comparable type comparison
	if new.From != old.From {
		diff["From"] = new.From
	}

	// Compare To

	// Comparable type comparison
	if new.To != old.To {
		diff["To"] = new.To
	}

	// Compare User

	// Comparable type comparison
	if new.User != old.User {
		diff["User"] = new.User
	}

	// Compare Ticket

	// Comparable type comparison
	if new.Ticket != old.Ticket {
		diff["Ticket"] = new.Ticket
	}

	// Compare File

	// Comparable type comparison
	if new.File != old.File {
		diff["File"] = new.File
	}

	// Compare Files

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Files, old.Files) {
		diff["Files"] = new.Files
	}

	// Compare Preview

	// Comparable type comparison
	if new.Preview != old.Preview {
		diff["Preview"] = new.Preview
	}

	// Compare Thumbnail

	// Comparable type comparison
	if new.Thumbnail != old.Thumbnail {
		diff["Thumbnail"] = new.Thumbnail
	}

	// Compare QuotedMessage

	// Comparable type comparison
	if new.QuotedMessage != old.QuotedMessage {
		diff["QuotedMessage"] = new.QuotedMessage
	}

	// Compare TicketUser

	// Comparable type comparison
	if new.TicketUser != old.TicketUser {
		diff["TicketUser"] = new.TicketUser
	}

	// Compare TicketDepartment

	// Comparable type comparison
	if new.TicketDepartment != old.TicketDepartment {
		diff["TicketDepartment"] = new.TicketDepartment
	}

	// Compare TicketTransfer

	// Comparable type comparison
	if new.TicketTransfer != old.TicketTransfer {
		diff["TicketTransfer"] = new.TicketTransfer
	}

	// Compare WhatsappBusinessTemplate

	// Comparable type comparison
	if new.WhatsappBusinessTemplate != old.WhatsappBusinessTemplate {
		diff["WhatsappBusinessTemplate"] = new.WhatsappBusinessTemplate
	}

	return diff
}

// Diff compares this Organization instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Organization) Diff(old *Organization) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare Document

	// Simple type comparison
	if new.Document != old.Document {
		diff["Document"] = new.Document
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare People

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.People, old.People) {
		diff["People"] = new.People
	}

	// Compare Users

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Users, old.Users) {
		diff["Users"] = new.Users
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this Person instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Person) Diff(old *Person) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare Document

	// Simple type comparison
	if new.Document != old.Document {
		diff["Document"] = new.Document
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Contacts

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Contacts, old.Contacts) {
		diff["Contacts"] = new.Contacts
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this Question instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Question) Diff(old *Question) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["Type"] = new.Type
	}

	// Compare QuestionMessage

	// Simple type comparison
	if new.QuestionMessage != old.QuestionMessage {
		diff["QuestionMessage"] = new.QuestionMessage
	}

	// Compare Duration

	// Simple type comparison
	if new.Duration != old.Duration {
		diff["Duration"] = new.Duration
	}

	// Compare Tries

	// Simple type comparison
	if new.Tries != old.Tries {
		diff["Tries"] = new.Tries
	}

	// Compare SuccessMessage

	// Simple type comparison
	if new.SuccessMessage != old.SuccessMessage {
		diff["SuccessMessage"] = new.SuccessMessage
	}

	// Compare InvalidMessage

	// Simple type comparison
	if new.InvalidMessage != old.InvalidMessage {
		diff["InvalidMessage"] = new.InvalidMessage
	}

	// Compare ReasonMessage

	// Simple type comparison
	if new.ReasonMessage != old.ReasonMessage {
		diff["ReasonMessage"] = new.ReasonMessage
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare QuestionId

	// UUID comparison

	// Direct UUID comparison
	if new.QuestionId != old.QuestionId {
		diff["QuestionId"] = new.QuestionId
	}

	// Compare Answer

	// Comparable type comparison
	if new.Answer != old.Answer {
		diff["Answer"] = new.Answer
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this WebchatFile instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *WebchatFile) Diff(old *WebchatFile) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Base64Url

	// Simple type comparison
	if new.Base64Url != old.Base64Url {
		diff["base64Url"] = new.Base64Url
	}

	// Compare Mimetype

	// Simple type comparison
	if new.Mimetype != old.Mimetype {
		diff["mimetype"] = new.Mimetype
	}

	// Compare FileName

	// Simple type comparison
	if new.FileName != old.FileName {
		diff["fileName"] = new.FileName
	}

	return diff
}

// Diff compares this WebchatData instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *WebchatData) Diff(old *WebchatData) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// Simple type comparison
	if new.Id != old.Id {
		diff["id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["name"] = new.Name
	}

	// Compare Phone

	// Simple type comparison
	if new.Phone != old.Phone {
		diff["phone"] = new.Phone
	}

	// Compare Telegram

	// Simple type comparison
	if new.Telegram != old.Telegram {
		diff["telegram"] = new.Telegram
	}

	// Compare IsOpenForm

	// Simple type comparison
	if new.IsOpenForm != old.IsOpenForm {
		diff["isOpenForm"] = new.IsOpenForm
	}

	// Compare IsOpenChat

	// Simple type comparison
	if new.IsOpenChat != old.IsOpenChat {
		diff["isOpenChat"] = new.IsOpenChat
	}

	// Compare File

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle direct struct (not pointer) - use attribute-by-attribute diff
	FileDiff := new.File.Diff(&old.File)
	if len(FileDiff) > 0 {
		jsonValue, err := sonic.Marshal(FileDiff)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["file"] = gorm.Expr("? || ?", clause.Column{Name: "file"}, string(jsonValue))
		} else if err != nil {
			// Fallback to regular assignment if JSON marshaling fails
			diff["file"] = new.File
		}
	}

	// Compare GoogleId

	// Simple type comparison
	if new.GoogleId != old.GoogleId {
		diff["googleId"] = new.GoogleId
	}

	return diff
}

// Diff compares this ServiceSettings instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ServiceSettings) Diff(old *ServiceSettings) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare ShouldOpenTicketForGroups

	// Simple type comparison
	if new.ShouldOpenTicketForGroups != old.ShouldOpenTicketForGroups {
		diff["shouldOpenTicketForGroups"] = new.ShouldOpenTicketForGroups
	}

	// Compare ReactionsEnabled

	// Simple type comparison
	if new.ReactionsEnabled != old.ReactionsEnabled {
		diff["reactionsEnabled"] = new.ReactionsEnabled
	}

	return diff
}

// Diff compares this ServiceDataStatus instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ServiceDataStatus) Diff(old *ServiceDataStatus) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare IsSyncing

	// Simple type comparison
	if new.IsSyncing != old.IsSyncing {
		diff["isSyncing"] = new.IsSyncing
	}

	// Compare IsConnected

	// Simple type comparison
	if new.IsConnected != old.IsConnected {
		diff["isConnected"] = new.IsConnected
	}

	// Compare IsStarting

	// Simple type comparison
	if new.IsStarting != old.IsStarting {
		diff["isStarting"] = new.IsStarting
	}

	// Compare IsStarted

	// Simple type comparison
	if new.IsStarted != old.IsStarted {
		diff["isStarted"] = new.IsStarted
	}

	// Compare BatteryLevel

	// Simple type comparison
	if new.BatteryLevel != old.BatteryLevel {
		diff["batteryLevel"] = new.BatteryLevel
	}

	// Compare IsCharging

	// Simple type comparison
	if new.IsCharging != old.IsCharging {
		diff["isCharging"] = new.IsCharging
	}

	// Compare IsConflicted

	// Simple type comparison
	if new.IsConflicted != old.IsConflicted {
		diff["isConflicted"] = new.IsConflicted
	}

	// Compare IsLoading

	// Simple type comparison
	if new.IsLoading != old.IsLoading {
		diff["isLoading"] = new.IsLoading
	}

	// Compare IsOnChatPage

	// Simple type comparison
	if new.IsOnChatPage != old.IsOnChatPage {
		diff["isOnChatPage"] = new.IsOnChatPage
	}

	// Compare EnteredQrCodePageAt

	// Time comparison

	// Pointer to time comparison
	if (new.EnteredQrCodePageAt == nil) != (old.EnteredQrCodePageAt == nil) || (new.EnteredQrCodePageAt != nil && !new.EnteredQrCodePageAt.Equal(*old.EnteredQrCodePageAt)) {
		diff["enteredQrCodePageAt"] = new.EnteredQrCodePageAt
	}

	// Compare DisconnectedAt

	// Time comparison

	// Pointer to time comparison
	if (new.DisconnectedAt == nil) != (old.DisconnectedAt == nil) || (new.DisconnectedAt != nil && !new.DisconnectedAt.Equal(*old.DisconnectedAt)) {
		diff["disconnectedAt"] = new.DisconnectedAt
	}

	// Compare IsOnQrPage

	// Simple type comparison
	if new.IsOnQrPage != old.IsOnQrPage {
		diff["isOnQrPage"] = new.IsOnQrPage
	}

	// Compare IsPhoneAuthed

	// Simple type comparison
	if new.IsPhoneAuthed != old.IsPhoneAuthed {
		diff["isPhoneAuthed"] = new.IsPhoneAuthed
	}

	// Compare IsPhoneConnected

	// Simple type comparison
	if new.IsPhoneConnected != old.IsPhoneConnected {
		diff["isPhoneConnected"] = new.IsPhoneConnected
	}

	// Compare IsQrCodeExpired

	// Simple type comparison
	if new.IsQrCodeExpired != old.IsQrCodeExpired {
		diff["isQrCodeExpired"] = new.IsQrCodeExpired
	}

	// Compare IsWaitingForPhoneInternet

	// Simple type comparison
	if new.IsWaitingForPhoneInternet != old.IsWaitingForPhoneInternet {
		diff["isWaitingForPhoneInternet"] = new.IsWaitingForPhoneInternet
	}

	// Compare IsWebConnected

	// Simple type comparison
	if new.IsWebConnected != old.IsWebConnected {
		diff["isWebConnected"] = new.IsWebConnected
	}

	// Compare IsWebSyncing

	// Simple type comparison
	if new.IsWebSyncing != old.IsWebSyncing {
		diff["isWebSyncing"] = new.IsWebSyncing
	}

	// Compare Mode

	// Simple type comparison
	if new.Mode != old.Mode {
		diff["mode"] = new.Mode
	}

	// Compare MyId

	// Simple type comparison
	if new.MyId != old.MyId {
		diff["myId"] = new.MyId
	}

	// Compare MyName

	// Simple type comparison
	if new.MyName != old.MyName {
		diff["myName"] = new.MyName
	}

	// Compare MyNumber

	// Simple type comparison
	if new.MyNumber != old.MyNumber {
		diff["myNumber"] = new.MyNumber
	}

	// Compare NeedsCharging

	// Simple type comparison
	if new.NeedsCharging != old.NeedsCharging {
		diff["needsCharging"] = new.NeedsCharging
	}

	// Compare QrCodeExpiresAt

	// Time comparison

	// Pointer to time comparison
	if (new.QrCodeExpiresAt == nil) != (old.QrCodeExpiresAt == nil) || (new.QrCodeExpiresAt != nil && !new.QrCodeExpiresAt.Equal(*old.QrCodeExpiresAt)) {
		diff["qrCodeExpiresAt"] = new.QrCodeExpiresAt
	}

	// Compare QrCodeUrl

	// Simple type comparison
	if new.QrCodeUrl != old.QrCodeUrl {
		diff["qrCodeUrl"] = new.QrCodeUrl
	}

	// Compare State

	// Simple type comparison
	if new.State != old.State {
		diff["state"] = new.State
	}

	// Compare Timestamp

	// Simple type comparison
	if new.Timestamp != old.Timestamp {
		diff["timestamp"] = new.Timestamp
	}

	// Compare WaVersion

	// Simple type comparison
	if new.WaVersion != old.WaVersion {
		diff["waVersion"] = new.WaVersion
	}

	return diff
}

// Diff compares this ServiceData instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ServiceData) Diff(old *ServiceData) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Error

	// Simple type comparison
	if new.Error != old.Error {
		diff["error"] = new.Error
	}

	// Compare SyncCount

	// Simple type comparison
	if new.SyncCount != old.SyncCount {
		diff["syncCount"] = new.SyncCount
	}

	// Compare MyId

	// Simple type comparison
	if new.MyId != old.MyId {
		diff["myId"] = new.MyId
	}

	// Compare Status

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Status == nil && old.Status != nil {
		// new is nil, old is not nil - set to null
		diff["status"] = nil
	} else if new.Status != nil && old.Status == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Status)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["status"] = gorm.Expr("? || ?", clause.Column{Name: "status"}, string(jsonValue))
		} else if err != nil {
			diff["status"] = new.Status
		}
	} else if new.Status != nil && old.Status != nil {
		// Both are not nil - use attribute-by-attribute diff
		StatusDiff := new.Status.Diff(old.Status)
		if len(StatusDiff) > 0 {
			jsonValue, err := sonic.Marshal(StatusDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["status"] = gorm.Expr("? || ?", clause.Column{Name: "status"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["status"] = new.Status
			}
		}
	}

	// Compare DriverId

	// Simple type comparison
	if new.DriverId != old.DriverId {
		diff["driverId"] = new.DriverId
	}

	// Compare BusinessId

	// Simple type comparison
	if new.BusinessId != old.BusinessId {
		diff["businessId"] = new.BusinessId
	}

	// Compare ProviderType

	// Simple type comparison
	if new.ProviderType != old.ProviderType {
		diff["providerType"] = new.ProviderType
	}

	// Compare AppName

	// Simple type comparison
	if new.AppName != old.AppName {
		diff["appName"] = new.AppName
	}

	// Compare Phone

	// Simple type comparison
	if new.Phone != old.Phone {
		diff["phone"] = new.Phone
	}

	return diff
}

// Diff compares this ServiceInternalData instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *ServiceInternalData) Diff(old *ServiceInternalData) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare ApiKey

	// Simple type comparison
	if new.ApiKey != old.ApiKey {
		diff["apiKey"] = new.ApiKey
	}

	// Compare Id

	// Simple type comparison
	if new.Id != old.Id {
		diff["id"] = new.Id
	}

	// Compare Phone

	// Simple type comparison
	if new.Phone != old.Phone {
		diff["phone"] = new.Phone
	}

	// Compare PartnerToken

	// Simple type comparison
	if new.PartnerToken != old.PartnerToken {
		diff["partnerToken"] = new.PartnerToken
	}

	// Compare AppToken

	// Simple type comparison
	if new.AppToken != old.AppToken {
		diff["appToken"] = new.AppToken
	}

	// Compare Token

	// Simple type comparison
	if new.Token != old.Token {
		diff["token"] = new.Token
	}

	return diff
}

// Diff compares this Service instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Service) Diff(old *Service) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare IsArchived

	// Simple type comparison
	if new.IsArchived != old.IsArchived {
		diff["IsArchived"] = new.IsArchived
	}

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["Type"] = new.Type
	}

	// Compare Data

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Data == nil && old.Data != nil {
		// new is nil, old is not nil - set to null
		diff["Data"] = nil
	} else if new.Data != nil && old.Data == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Data)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Data"] = gorm.Expr("? || ?", clause.Column{Name: "data"}, string(jsonValue))
		} else if err != nil {
			diff["Data"] = new.Data
		}
	} else if new.Data != nil && old.Data != nil {
		// Both are not nil - use attribute-by-attribute diff
		DataDiff := new.Data.Diff(old.Data)
		if len(DataDiff) > 0 {
			jsonValue, err := sonic.Marshal(DataDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Data"] = gorm.Expr("? || ?", clause.Column{Name: "data"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Data"] = new.Data
			}
		}
	}

	// Compare InternalData

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.InternalData == nil && old.InternalData != nil {
		// new is nil, old is not nil - set to null
		diff["InternalData"] = nil
	} else if new.InternalData != nil && old.InternalData == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.InternalData)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["InternalData"] = gorm.Expr("? || ?", clause.Column{Name: "internal_data"}, string(jsonValue))
		} else if err != nil {
			diff["InternalData"] = new.InternalData
		}
	} else if new.InternalData != nil && old.InternalData != nil {
		// Both are not nil - use attribute-by-attribute diff
		InternalDataDiff := new.InternalData.Diff(old.InternalData)
		if len(InternalDataDiff) > 0 {
			jsonValue, err := sonic.Marshal(InternalDataDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["InternalData"] = gorm.Expr("? || ?", clause.Column{Name: "internal_data"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["InternalData"] = new.InternalData
			}
		}
	}

	// Compare Settings

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Settings == nil && old.Settings != nil {
		// new is nil, old is not nil - set to null
		diff["Settings"] = nil
	} else if new.Settings != nil && old.Settings == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Settings)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Settings"] = gorm.Expr("? || ?", clause.Column{Name: "settings"}, string(jsonValue))
		} else if err != nil {
			diff["Settings"] = new.Settings
		}
	} else if new.Settings != nil && old.Settings != nil {
		// Both are not nil - use attribute-by-attribute diff
		SettingsDiff := new.Settings.Diff(old.Settings)
		if len(SettingsDiff) > 0 {
			jsonValue, err := sonic.Marshal(SettingsDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Settings"] = gorm.Expr("? || ?", clause.Column{Name: "settings"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Settings"] = new.Settings
			}
		}
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare DefaultDepartmentId

	// UUID comparison

	// Direct UUID comparison
	if new.DefaultDepartmentId != old.DefaultDepartmentId {
		diff["DefaultDepartmentId"] = new.DefaultDepartmentId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Contacts

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Contacts, old.Contacts) {
		diff["Contacts"] = new.Contacts
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	// Compare ArchivedAt

	// Time comparison

	// Pointer to time comparison
	if (new.ArchivedAt == nil) != (old.ArchivedAt == nil) || (new.ArchivedAt != nil && !new.ArchivedAt.Equal(*old.ArchivedAt)) {
		diff["ArchivedAt"] = new.ArchivedAt
	}

	// Compare BotId

	// UUID comparison

	// Direct UUID comparison
	if new.BotId != old.BotId {
		diff["BotId"] = new.BotId
	}

	return diff
}

// Diff compares this Sticker instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Sticker) Diff(old *Sticker) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["Type"] = new.Type
	}

	// Compare OriginFileChecksum

	// Simple type comparison
	if new.OriginFileChecksum != old.OriginFileChecksum {
		diff["OriginFileChecksum"] = new.OriginFileChecksum
	}

	// Compare OriginFilehash

	// Simple type comparison
	if new.OriginFilehash != old.OriginFilehash {
		diff["OriginFilehash"] = new.OriginFilehash
	}

	// Compare OriginMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.OriginMessageId != old.OriginMessageId {
		diff["OriginMessageId"] = new.OriginMessageId
	}

	// Compare UserId

	// UUID comparison

	// Direct UUID comparison
	if new.UserId != old.UserId {
		diff["UserId"] = new.UserId
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	// Compare OriginMessage

	// Comparable type comparison
	if new.OriginMessage != old.OriginMessage {
		diff["OriginMessage"] = new.OriginMessage
	}

	// Compare User

	// Comparable type comparison
	if new.User != old.User {
		diff["User"] = new.User
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare File

	// Comparable type comparison
	if new.File != old.File {
		diff["File"] = new.File
	}

	return diff
}

// Diff compares this StickerUser instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *StickerUser) Diff(old *StickerUser) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare StickerId

	// UUID comparison

	// Direct UUID comparison
	if new.StickerId != old.StickerId {
		diff["StickerId"] = new.StickerId
	}

	// Compare UserId

	// UUID comparison

	// Direct UUID comparison
	if new.UserId != old.UserId {
		diff["UserId"] = new.UserId
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare LastSendAt

	// Time comparison

	// Pointer to time comparison
	if (new.LastSendAt == nil) != (old.LastSendAt == nil) || (new.LastSendAt != nil && !new.LastSendAt.Equal(*old.LastSendAt)) {
		diff["LastSendAt"] = new.LastSendAt
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare Sticker

	// Comparable type comparison
	if new.Sticker != old.Sticker {
		diff["Sticker"] = new.Sticker
	}

	// Compare User

	// Comparable type comparison
	if new.User != old.User {
		diff["User"] = new.User
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare File

	// Comparable type comparison
	if new.File != old.File {
		diff["File"] = new.File
	}

	return diff
}

// Diff compares this Tag instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Tag) Diff(old *Tag) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Label

	// Simple type comparison
	if new.Label != old.Label {
		diff["Label"] = new.Label
	}

	// Compare BackgroundColor

	// Simple type comparison
	if new.BackgroundColor != old.BackgroundColor {
		diff["BackgroundColor"] = new.BackgroundColor
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Contacts

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Contacts, old.Contacts) {
		diff["Contacts"] = new.Contacts
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this TicketMetrics instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *TicketMetrics) Diff(old *TicketMetrics) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare WaitingTime

	// Simple type comparison
	if new.WaitingTime != old.WaitingTime {
		diff["waitingTime"] = new.WaitingTime
	}

	// Compare WaitingTimeAfterBot

	// Simple type comparison
	if new.WaitingTimeAfterBot != old.WaitingTimeAfterBot {
		diff["waitingTimeAfterBot"] = new.WaitingTimeAfterBot
	}

	// Compare MessagingTime

	// Simple type comparison
	if new.MessagingTime != old.MessagingTime {
		diff["messagingTime"] = new.MessagingTime
	}

	// Compare TicketTime

	// Simple type comparison
	if new.TicketTime != old.TicketTime {
		diff["ticketTime"] = new.TicketTime
	}

	// Compare WaitingTimeTransfersSum

	// Simple type comparison
	if new.WaitingTimeTransfersSum != old.WaitingTimeTransfersSum {
		diff["waitingTimeTransfersSum"] = new.WaitingTimeTransfersSum
	}

	// Compare TicketTransferCount

	// Simple type comparison
	if new.TicketTransferCount != old.TicketTransferCount {
		diff["ticketTransferCount"] = new.TicketTransferCount
	}

	// Compare WaitingTimeTransfersAvg

	// Simple type comparison
	if new.WaitingTimeTransfersAvg != old.WaitingTimeTransfersAvg {
		diff["waitingTimeTransfersAvg"] = new.WaitingTimeTransfersAvg
	}

	// Compare IsActiveTicket

	// Simple type comparison
	if new.IsActiveTicket != old.IsActiveTicket {
		diff["isActiveTicket"] = new.IsActiveTicket
	}

	return diff
}

// Diff compares this Ticket instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *Ticket) Diff(old *Ticket) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare IsOpen

	// Simple type comparison
	if new.IsOpen != old.IsOpen {
		diff["IsOpen"] = new.IsOpen
	}

	// Compare Comments

	// Simple type comparison
	if new.Comments != old.Comments {
		diff["Comments"] = new.Comments
	}

	// Compare Protocol

	// Simple type comparison
	if new.Protocol != old.Protocol {
		diff["Protocol"] = new.Protocol
	}

	// Compare Origin

	// Simple type comparison
	if new.Origin != old.Origin {
		diff["Origin"] = new.Origin
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare DepartmentId

	// UUID comparison

	// Direct UUID comparison
	if new.DepartmentId != old.DepartmentId {
		diff["DepartmentId"] = new.DepartmentId
	}

	// Compare ContactId

	// UUID comparison

	// Direct UUID comparison
	if new.ContactId != old.ContactId {
		diff["ContactId"] = new.ContactId
	}

	// Compare UserId

	// UUID comparison

	// Direct UUID comparison
	if new.UserId != old.UserId {
		diff["UserId"] = new.UserId
	}

	// Compare FirstMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.FirstMessageId != old.FirstMessageId {
		diff["FirstMessageId"] = new.FirstMessageId
	}

	// Compare LastMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.LastMessageId != old.LastMessageId {
		diff["LastMessageId"] = new.LastMessageId
	}

	// Compare CurrentTicketTransferId

	// UUID comparison

	// Direct UUID comparison
	if new.CurrentTicketTransferId != old.CurrentTicketTransferId {
		diff["CurrentTicketTransferId"] = new.CurrentTicketTransferId
	}

	// Compare StartedAt

	// Time comparison

	// Pointer to time comparison
	if (new.StartedAt == nil) != (old.StartedAt == nil) || (new.StartedAt != nil && !new.StartedAt.Equal(*old.StartedAt)) {
		diff["StartedAt"] = new.StartedAt
	}

	// Compare EndedAt

	// Time comparison

	// Pointer to time comparison
	if (new.EndedAt == nil) != (old.EndedAt == nil) || (new.EndedAt != nil && !new.EndedAt.Equal(*old.EndedAt)) {
		diff["EndedAt"] = new.EndedAt
	}

	// Compare Metrics

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Metrics == nil && old.Metrics != nil {
		// new is nil, old is not nil - set to null
		diff["Metrics"] = nil
	} else if new.Metrics != nil && old.Metrics == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Metrics)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Metrics"] = gorm.Expr("? || ?", clause.Column{Name: "metrics"}, string(jsonValue))
		} else if err != nil {
			diff["Metrics"] = new.Metrics
		}
	} else if new.Metrics != nil && old.Metrics != nil {
		// Both are not nil - use attribute-by-attribute diff
		MetricsDiff := new.Metrics.Diff(old.Metrics)
		if len(MetricsDiff) > 0 {
			jsonValue, err := sonic.Marshal(MetricsDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Metrics"] = gorm.Expr("? || ?", clause.Column{Name: "metrics"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Metrics"] = new.Metrics
			}
		}
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Contact

	// Comparable type comparison
	if new.Contact != old.Contact {
		diff["Contact"] = new.Contact
	}

	// Compare User

	// Comparable type comparison
	if new.User != old.User {
		diff["User"] = new.User
	}

	// Compare Department

	// Comparable type comparison
	if new.Department != old.Department {
		diff["Department"] = new.Department
	}

	// Compare Messages

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Messages, old.Messages) {
		diff["Messages"] = new.Messages
	}

	// Compare FirstMessage

	// Comparable type comparison
	if new.FirstMessage != old.FirstMessage {
		diff["FirstMessage"] = new.FirstMessage
	}

	// Compare LastMessage

	// Comparable type comparison
	if new.LastMessage != old.LastMessage {
		diff["LastMessage"] = new.LastMessage
	}

	// Compare TicketTransfers

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.TicketTransfers, old.TicketTransfers) {
		diff["TicketTransfers"] = new.TicketTransfers
	}

	// Compare CurrentTicketTransfer

	// Comparable type comparison
	if new.CurrentTicketTransfer != old.CurrentTicketTransfer {
		diff["CurrentTicketTransfer"] = new.CurrentTicketTransfer
	}

	// Compare TicketTopics

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.TicketTopics, old.TicketTopics) {
		diff["TicketTopics"] = new.TicketTopics
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare IsDistributing

	// Simple type comparison
	if new.IsDistributing != old.IsDistributing {
		diff["IsDistributing"] = new.IsDistributing
	}

	// Compare Count

	// Simple type comparison
	if new.Count != old.Count {
		diff["Count"] = new.Count
	}

	// Compare Answers

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Answers, old.Answers) {
		diff["Answers"] = new.Answers
	}

	return diff
}

// Diff compares this TicketTicketTopic instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *TicketTicketTopic) Diff(old *TicketTicketTopic) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare TicketId

	// UUID comparison

	// Direct UUID comparison
	if new.TicketId != old.TicketId {
		diff["TicketId"] = new.TicketId
	}

	// Compare TicketTopicId

	// UUID comparison

	// Direct UUID comparison
	if new.TicketTopicId != old.TicketTopicId {
		diff["TicketTopicId"] = new.TicketTopicId
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	return diff
}

// Diff compares this TicketTopic instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *TicketTopic) Diff(old *TicketTopic) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare ArchivedAt

	// Time comparison

	// Pointer to time comparison
	if (new.ArchivedAt == nil) != (old.ArchivedAt == nil) || (new.ArchivedAt != nil && !new.ArchivedAt.Equal(*old.ArchivedAt)) {
		diff["ArchivedAt"] = new.ArchivedAt
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Tickets

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Tickets, old.Tickets) {
		diff["Tickets"] = new.Tickets
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	return diff
}

// Diff compares this TransferMetrics instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *TransferMetrics) Diff(old *TransferMetrics) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare WaitingTime

	// Simple type comparison
	if new.WaitingTime != old.WaitingTime {
		diff["waitingTime"] = new.WaitingTime
	}

	// Compare MessagingTime

	// Simple type comparison
	if new.MessagingTime != old.MessagingTime {
		diff["messagingTime"] = new.MessagingTime
	}

	// Compare TicketTime

	// Simple type comparison
	if new.TicketTime != old.TicketTime {
		diff["ticketTime"] = new.TicketTime
	}

	// Compare TransferredByBot

	// Simple type comparison
	if new.TransferredByBot != old.TransferredByBot {
		diff["transferredByBot"] = new.TransferredByBot
	}

	return diff
}

// Diff compares this TicketTransfer instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *TicketTransfer) Diff(old *TicketTransfer) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Action

	// Simple type comparison
	if new.Action != old.Action {
		diff["Action"] = new.Action
	}

	// Compare Comments

	// Simple type comparison
	if new.Comments != old.Comments {
		diff["Comments"] = new.Comments
	}

	// Compare Metrics

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Metrics == nil && old.Metrics != nil {
		// new is nil, old is not nil - set to null
		diff["Metrics"] = nil
	} else if new.Metrics != nil && old.Metrics == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Metrics)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Metrics"] = gorm.Expr("? || ?", clause.Column{Name: "metrics"}, string(jsonValue))
		} else if err != nil {
			diff["Metrics"] = new.Metrics
		}
	} else if new.Metrics != nil && old.Metrics != nil {
		// Both are not nil - use attribute-by-attribute diff
		MetricsDiff := new.Metrics.Diff(old.Metrics)
		if len(MetricsDiff) > 0 {
			jsonValue, err := sonic.Marshal(MetricsDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["Metrics"] = gorm.Expr("? || ?", clause.Column{Name: "metrics"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["Metrics"] = new.Metrics
			}
		}
	}

	// Compare ToUserId

	// UUID comparison

	// Direct UUID comparison
	if new.ToUserId != old.ToUserId {
		diff["ToUserId"] = new.ToUserId
	}

	// Compare FromUserId

	// UUID comparison

	// Direct UUID comparison
	if new.FromUserId != old.FromUserId {
		diff["FromUserId"] = new.FromUserId
	}

	// Compare FromDepartmentId

	// UUID comparison

	// Direct UUID comparison
	if new.FromDepartmentId != old.FromDepartmentId {
		diff["FromDepartmentId"] = new.FromDepartmentId
	}

	// Compare ToDepartmentId

	// UUID comparison

	// Direct UUID comparison
	if new.ToDepartmentId != old.ToDepartmentId {
		diff["ToDepartmentId"] = new.ToDepartmentId
	}

	// Compare StartedAt

	// Time comparison

	// Pointer to time comparison
	if (new.StartedAt == nil) != (old.StartedAt == nil) || (new.StartedAt != nil && !new.StartedAt.Equal(*old.StartedAt)) {
		diff["StartedAt"] = new.StartedAt
	}

	// Compare EndedAt

	// Time comparison

	// Pointer to time comparison
	if (new.EndedAt == nil) != (old.EndedAt == nil) || (new.EndedAt != nil && !new.EndedAt.Equal(*old.EndedAt)) {
		diff["EndedAt"] = new.EndedAt
	}

	// Compare TransferredMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.TransferredMessageId != old.TransferredMessageId {
		diff["TransferredMessageId"] = new.TransferredMessageId
	}

	// Compare FirstMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.FirstMessageId != old.FirstMessageId {
		diff["FirstMessageId"] = new.FirstMessageId
	}

	// Compare LastMessageId

	// UUID comparison

	// Direct UUID comparison
	if new.LastMessageId != old.LastMessageId {
		diff["LastMessageId"] = new.LastMessageId
	}

	// Compare ByUserId

	// UUID comparison

	// Direct UUID comparison
	if new.ByUserId != old.ByUserId {
		diff["ByUserId"] = new.ByUserId
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare FromDistribution

	// Simple type comparison
	if new.FromDistribution != old.FromDistribution {
		diff["FromDistribution"] = new.FromDistribution
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare ToDepartment

	// Comparable type comparison
	if new.ToDepartment != old.ToDepartment {
		diff["ToDepartment"] = new.ToDepartment
	}

	// Compare FromDepartment

	// Comparable type comparison
	if new.FromDepartment != old.FromDepartment {
		diff["FromDepartment"] = new.FromDepartment
	}

	// Compare ToUser

	// Comparable type comparison
	if new.ToUser != old.ToUser {
		diff["ToUser"] = new.ToUser
	}

	// Compare FromUser

	// Comparable type comparison
	if new.FromUser != old.FromUser {
		diff["FromUser"] = new.FromUser
	}

	// Compare TransferredMessage

	// Comparable type comparison
	if new.TransferredMessage != old.TransferredMessage {
		diff["TransferredMessage"] = new.TransferredMessage
	}

	// Compare FirstMessage

	// Comparable type comparison
	if new.FirstMessage != old.FirstMessage {
		diff["FirstMessage"] = new.FirstMessage
	}

	// Compare LastMessage

	// Comparable type comparison
	if new.LastMessage != old.LastMessage {
		diff["LastMessage"] = new.LastMessage
	}

	// Compare ByUser

	// Comparable type comparison
	if new.ByUser != old.ByUser {
		diff["ByUser"] = new.ByUser
	}

	// Compare TicketId

	// UUID comparison

	// Direct UUID comparison
	if new.TicketId != old.TicketId {
		diff["TicketId"] = new.TicketId
	}

	// Compare Ticket

	// Comparable type comparison
	if new.Ticket != old.Ticket {
		diff["Ticket"] = new.Ticket
	}

	return diff
}

// Diff compares this User instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *User) Diff(old *User) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare Email

	// Simple type comparison
	if new.Email != old.Email {
		diff["Email"] = new.Email
	}

	// Compare PhoneNumber

	// Simple type comparison
	if new.PhoneNumber != old.PhoneNumber {
		diff["PhoneNumber"] = new.PhoneNumber
	}

	// Compare Branch

	// Simple type comparison
	if new.Branch != old.Branch {
		diff["Branch"] = new.Branch
	}

	// Compare Password

	// Simple type comparison
	if new.Password != old.Password {
		diff["Password"] = new.Password
	}

	// Compare ActivationToken

	// Simple type comparison
	if new.ActivationToken != old.ActivationToken {
		diff["ActivationToken"] = new.ActivationToken
	}

	// Compare ResetPasswordToken

	// Simple type comparison
	if new.ResetPasswordToken != old.ResetPasswordToken {
		diff["ResetPasswordToken"] = new.ResetPasswordToken
	}

	// Compare IsSuperAdmin

	// Simple type comparison
	if new.IsSuperAdmin != old.IsSuperAdmin {
		diff["IsSuperAdmin"] = new.IsSuperAdmin
	}

	// Compare IsClientUser

	// Simple type comparison
	if new.IsClientUser != old.IsClientUser {
		diff["IsClientUser"] = new.IsClientUser
	}

	// Compare Active

	// Simple type comparison
	if new.Active != old.Active {
		diff["Active"] = new.Active
	}

	// Compare IsFirstLogin

	// Simple type comparison
	if new.IsFirstLogin != old.IsFirstLogin {
		diff["IsFirstLogin"] = new.IsFirstLogin
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare Departments

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Departments, old.Departments) {
		diff["Departments"] = new.Departments
	}

	// Compare DepartmentsDefault

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.DepartmentsDefault, old.DepartmentsDefault) {
		diff["DepartmentsDefault"] = new.DepartmentsDefault
	}

	// Compare TimetableId

	// UUID comparison

	// Direct UUID comparison
	if new.TimetableId != old.TimetableId {
		diff["TimetableId"] = new.TimetableId
	}

	// Compare Data

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// Use bytes.Equal for datatypes.JSON ([]byte underlying type)
	if !bytes.Equal([]byte(new.Data), []byte(old.Data)) {
		jsonValue, err := sonic.Marshal(new.Data)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Data"] = gorm.Expr("? || ?", clause.Column{Name: "data"}, string(jsonValue))
		} else if err != nil {
			// Fallback to regular assignment if JSON marshaling fails
			diff["Data"] = new.Data
		}
		// Skip adding to diff if JSON is empty (no-op update)
	}

	// Compare InternalChatToken

	// Simple type comparison
	if new.InternalChatToken != old.InternalChatToken {
		diff["InternalChatToken"] = new.InternalChatToken
	}

	// Compare ArchivedAt

	// Time comparison

	// Pointer to time comparison
	if (new.ArchivedAt == nil) != (old.ArchivedAt == nil) || (new.ArchivedAt != nil && !new.ArchivedAt.Equal(*old.ArchivedAt)) {
		diff["ArchivedAt"] = new.ArchivedAt
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare Status

	// Simple type comparison
	if new.Status != old.Status {
		diff["Status"] = new.Status
	}

	// Compare ClientsStatus

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// Use bytes.Equal for datatypes.JSON ([]byte underlying type)
	if !bytes.Equal([]byte(new.ClientsStatus), []byte(old.ClientsStatus)) {
		jsonValue, err := sonic.Marshal(new.ClientsStatus)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["ClientsStatus"] = gorm.Expr("? || ?", clause.Column{Name: "clients_status"}, string(jsonValue))
		} else if err != nil {
			// Fallback to regular assignment if JSON marshaling fails
			diff["ClientsStatus"] = new.ClientsStatus
		}
		// Skip adding to diff if JSON is empty (no-op update)
	}

	// Compare OfflineAt

	// Time comparison

	// Pointer to time comparison
	if (new.OfflineAt == nil) != (old.OfflineAt == nil) || (new.OfflineAt != nil && !new.OfflineAt.Equal(*old.OfflineAt)) {
		diff["OfflineAt"] = new.OfflineAt
	}

	// Compare IsActiveInternalChat

	// Simple type comparison
	if new.IsActiveInternalChat != old.IsActiveInternalChat {
		diff["IsActiveInternalChat"] = new.IsActiveInternalChat
	}

	// Compare PasswordExpiresAt

	// Time comparison

	// Pointer to time comparison
	if (new.PasswordExpiresAt == nil) != (old.PasswordExpiresAt == nil) || (new.PasswordExpiresAt != nil && !new.PasswordExpiresAt.Equal(*old.PasswordExpiresAt)) {
		diff["PasswordExpiresAt"] = new.PasswordExpiresAt
	}

	// Compare HasPasswordExpired

	// Simple type comparison
	if new.HasPasswordExpired != old.HasPasswordExpired {
		diff["HasPasswordExpired"] = new.HasPasswordExpired
	}

	// Compare OtpSecretKey

	// Simple type comparison
	if new.OtpSecretKey != old.OtpSecretKey {
		diff["OtpSecretKey"] = new.OtpSecretKey
	}

	// Compare OtpAuthActive

	// Simple type comparison
	if new.OtpAuthActive != old.OtpAuthActive {
		diff["OtpAuthActive"] = new.OtpAuthActive
	}

	// Compare Language

	// Simple type comparison
	if new.Language != old.Language {
		diff["Language"] = new.Language
	}

	// Compare CountTickets

	// Simple type comparison
	if new.CountTickets != old.CountTickets {
		diff["CountTickets"] = new.CountTickets
	}

	return diff
}

// Diff compares this WhatsappBusinessComponentParameterButton instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *WhatsappBusinessComponentParameterButton) Diff(old *WhatsappBusinessComponentParameterButton) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["type"] = new.Type
	}

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["text"] = new.Text
	}

	// Compare PhoneNumber

	// Simple type comparison
	if new.PhoneNumber != old.PhoneNumber {
		diff["phone_number"] = new.PhoneNumber
	}

	// Compare URL

	// Simple type comparison
	if new.URL != old.URL {
		diff["url"] = new.URL
	}

	// Compare Example

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Example, old.Example) {
		diff["example"] = new.Example
	}

	// Compare OtpType

	// Simple type comparison
	if new.OtpType != old.OtpType {
		diff["otp_type"] = new.OtpType
	}

	// Compare AutofillText

	// Simple type comparison
	if new.AutofillText != old.AutofillText {
		diff["autofill_text"] = new.AutofillText
	}

	// Compare PackageName

	// Simple type comparison
	if new.PackageName != old.PackageName {
		diff["package_name"] = new.PackageName
	}

	// Compare SignatureHash

	// Simple type comparison
	if new.SignatureHash != old.SignatureHash {
		diff["signature_hash"] = new.SignatureHash
	}

	return diff
}

// Diff compares this WhatsappBusinessComponentExample instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *WhatsappBusinessComponentExample) Diff(old *WhatsappBusinessComponentExample) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare HeaderHandle

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.HeaderHandle, old.HeaderHandle) {
		diff["header_handle"] = new.HeaderHandle
	}

	// Compare HeaderText

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.HeaderText, old.HeaderText) {
		diff["header_text"] = new.HeaderText
	}

	// Compare BodyText

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.BodyText, old.BodyText) {
		diff["body_text"] = new.BodyText
	}

	return diff
}

// Diff compares this WhatsappBusinessComponent instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *WhatsappBusinessComponent) Diff(old *WhatsappBusinessComponent) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Type

	// Simple type comparison
	if new.Type != old.Type {
		diff["type"] = new.Type
	}

	// Compare Format

	// Simple type comparison
	if new.Format != old.Format {
		diff["format"] = new.Format
	}

	// Compare Text

	// Simple type comparison
	if new.Text != old.Text {
		diff["text"] = new.Text
	}

	// Compare Buttons

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Buttons, old.Buttons) {
		diff["buttons"] = new.Buttons
	}

	// Compare Example

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle pointer to struct
	if new.Example == nil && old.Example != nil {
		// new is nil, old is not nil - set to null
		diff["example"] = nil
	} else if new.Example != nil && old.Example == nil {
		// new is not nil, old is nil - use entire new
		jsonValue, err := sonic.Marshal(new.Example)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["example"] = gorm.Expr("? || ?", clause.Column{Name: "example"}, string(jsonValue))
		} else if err != nil {
			diff["example"] = new.Example
		}
	} else if new.Example != nil && old.Example != nil {
		// Both are not nil - use attribute-by-attribute diff
		ExampleDiff := new.Example.Diff(old.Example)
		if len(ExampleDiff) > 0 {
			jsonValue, err := sonic.Marshal(ExampleDiff)
			if err == nil && !isEmptyJSON(string(jsonValue)) {
				diff["example"] = gorm.Expr("? || ?", clause.Column{Name: "example"}, string(jsonValue))
			} else if err != nil {
				// Fallback to regular assignment if JSON marshaling fails
				diff["example"] = new.Example
			}
		}
	}

	// Compare Params

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.Params, old.Params) {
		diff["params"] = new.Params
	}

	// Compare AddSecurityRecommendation

	// Simple type comparison
	if new.AddSecurityRecommendation != old.AddSecurityRecommendation {
		diff["add_security_recommendation"] = new.AddSecurityRecommendation
	}

	// Compare CodeExpirationMinutes

	// Simple type comparison
	if new.CodeExpirationMinutes != old.CodeExpirationMinutes {
		diff["code_expiration_minutes"] = new.CodeExpirationMinutes
	}

	// Compare ActiveCodeExpirationMinutes

	// Simple type comparison
	if new.ActiveCodeExpirationMinutes != old.ActiveCodeExpirationMinutes {
		diff["active_code_expiration_minutes"] = new.ActiveCodeExpirationMinutes
	}

	// Compare OtpType

	// Simple type comparison
	if new.OtpType != old.OtpType {
		diff["otp_type"] = new.OtpType
	}

	return diff
}

// Diff compares this WhatsappBusinessTemplate instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *WhatsappBusinessTemplate) Diff(old *WhatsappBusinessTemplate) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare InternalName

	// Simple type comparison
	if new.InternalName != old.InternalName {
		diff["InternalName"] = new.InternalName
	}

	// Compare Category

	// Simple type comparison
	if new.Category != old.Category {
		diff["Category"] = new.Category
	}

	// Compare Components

	// JSON field comparison - handle both datatypes.JSON and struct types with jsonb storage

	// JSON field comparison - attribute-by-attribute diff for struct types

	// Handle direct struct (not pointer) - use attribute-by-attribute diff
	ComponentsDiff := new.Components.Diff(&old.Components)
	if len(ComponentsDiff) > 0 {
		jsonValue, err := sonic.Marshal(ComponentsDiff)
		if err == nil && !isEmptyJSON(string(jsonValue)) {
			diff["Components"] = gorm.Expr("? || ?", clause.Column{Name: "components"}, string(jsonValue))
		} else if err != nil {
			// Fallback to regular assignment if JSON marshaling fails
			diff["Components"] = new.Components
		}
	}

	// Compare Language

	// Simple type comparison
	if new.Language != old.Language {
		diff["Language"] = new.Language
	}

	// Compare Name

	// Simple type comparison
	if new.Name != old.Name {
		diff["Name"] = new.Name
	}

	// Compare Namespace

	// Simple type comparison
	if new.Namespace != old.Namespace {
		diff["Namespace"] = new.Namespace
	}

	// Compare RejectedReason

	// Simple type comparison
	if new.RejectedReason != old.RejectedReason {
		diff["RejectedReason"] = new.RejectedReason
	}

	// Compare Status

	// Simple type comparison
	if new.Status != old.Status {
		diff["Status"] = new.Status
	}

	// Compare MessageType

	// Simple type comparison
	if new.MessageType != old.MessageType {
		diff["MessageType"] = new.MessageType
	}

	// Compare ServiceId

	// UUID comparison

	// Direct UUID comparison
	if new.ServiceId != old.ServiceId {
		diff["ServiceId"] = new.ServiceId
	}

	// Compare Service

	// Comparable type comparison
	if new.Service != old.Service {
		diff["Service"] = new.Service
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare IdGupshup

	// Simple type comparison
	if new.IdGupshup != old.IdGupshup {
		diff["IdGupshup"] = new.IdGupshup
	}

	// Compare Quality

	// Simple type comparison
	if new.Quality != old.Quality {
		diff["Quality"] = new.Quality
	}

	// Compare ArchivedAt

	// Time comparison

	// Pointer to time comparison
	if (new.ArchivedAt == nil) != (old.ArchivedAt == nil) || (new.ArchivedAt != nil && !new.ArchivedAt.Equal(*old.ArchivedAt)) {
		diff["ArchivedAt"] = new.ArchivedAt
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	// Compare DeletedAt

	// GORM DeletedAt comparison
	if new.DeletedAt != old.DeletedAt {
		diff["DeletedAt"] = new.DeletedAt
	}

	// Compare FileExample

	// Complex type comparison (slice, map, interface, etc.)
	if !reflect.DeepEqual(new.FileExample, old.FileExample) {
		diff["FileExample"] = new.FileExample
	}

	return diff
}

// Diff compares this WhatsappBusinessTemplateHistory instance (new) with another (old) and returns a map of differences
// with only the new values for fields that have changed.
// Usage: newValues = new.Diff(old)
// Returns nil if either pointer is nil.
func (new *WhatsappBusinessTemplateHistory) Diff(old *WhatsappBusinessTemplateHistory) map[string]interface{} {
	// Handle nil pointers
	if new == nil || old == nil {
		return nil
	}

	diff := make(map[string]interface{})

	// Compare Id

	// UUID comparison

	// Direct UUID comparison
	if new.Id != old.Id {
		diff["Id"] = new.Id
	}

	// Compare AccountId

	// UUID comparison

	// Direct UUID comparison
	if new.AccountId != old.AccountId {
		diff["AccountId"] = new.AccountId
	}

	// Compare Account

	// Comparable type comparison
	if new.Account != old.Account {
		diff["Account"] = new.Account
	}

	// Compare WabaTemplateId

	// UUID comparison

	// Direct UUID comparison
	if new.WabaTemplateId != old.WabaTemplateId {
		diff["WabaTemplateId"] = new.WabaTemplateId
	}

	// Compare WhatsappBusinessTemplate

	// Comparable type comparison
	if new.WhatsappBusinessTemplate != old.WhatsappBusinessTemplate {
		diff["WhatsappBusinessTemplate"] = new.WhatsappBusinessTemplate
	}

	// Compare StatusFrom

	// Simple type comparison
	if new.StatusFrom != old.StatusFrom {
		diff["StatusFrom"] = new.StatusFrom
	}

	// Compare StatusTo

	// Simple type comparison
	if new.StatusTo != old.StatusTo {
		diff["StatusTo"] = new.StatusTo
	}

	// Compare QualityFrom

	// Simple type comparison
	if new.QualityFrom != old.QualityFrom {
		diff["QualityFrom"] = new.QualityFrom
	}

	// Compare QualityTo

	// Simple type comparison
	if new.QualityTo != old.QualityTo {
		diff["QualityTo"] = new.QualityTo
	}

	// Compare CreatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.CreatedAt == nil) != (old.CreatedAt == nil) || (new.CreatedAt != nil && !new.CreatedAt.Equal(*old.CreatedAt)) {
		diff["CreatedAt"] = new.CreatedAt
	}

	// Compare UpdatedAt

	// Time comparison

	// Pointer to time comparison
	if (new.UpdatedAt == nil) != (old.UpdatedAt == nil) || (new.UpdatedAt != nil && !new.UpdatedAt.Equal(*old.UpdatedAt)) {
		diff["UpdatedAt"] = new.UpdatedAt
	}

	return diff
}
