package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// AutoPauseModeEnum define os possíveis modos de auto-pausa para campanhas
type AutoPauseModeEnum string

// Constantes para modos de auto-pausa
const (
	AutoPauseModeDisabled     AutoPauseModeEnum = "disabled"
	AutoPauseModeLowHealth    AutoPauseModeEnum = "low_health"
	AutoPauseModeMediumHealth AutoPauseModeEnum = "medium_health"
)

// @jsonb
type AccountFlags struct {
	DisableHsmLimit bool `json:"disable-hsm-limit"`
}

// @jsonb
type AccountAiPlan struct {
	Copilot       string `json:"copilot"`
	Summary       string `json:"summary"`
	MagicText     string `json:"magic-text"`
	Transcription int    `json:"transcription"`
}

// AccountPlan representa o plano da conta
// @jsonb
type AccountPlan struct {
	HsmUsedLimit       int            `json:"hsmUsedLimit"`
	HsmLimit           int            `json:"hsmLimit"`
	Services           map[string]int `json:"services"`
	Ai                 any            `json:"ai"`    // tem momento que é int tem momento que é AccountAiPlan
	Users              any            `json:"users"` // pode ser int ou string
	EmailNotifications any            `json:"emailNotifications"`
}

// AccountCampaignSettings representa as configurações de campanha na conta
// @jsonb
type AccountCampaignSettings struct {
	AutoPauseMode AutoPauseModeEnum `json:"auto-pause-mode"`
}

// @jsonb
type AccountSettings struct {
	DisableDefaultTicketTransfer bool                     `json:"disableDefaultTicketTransfer"`
	TicketsEnabled               bool                     `json:"ticketsEnabled"`
	ProtocolFormat               string                   `json:"string"`
	EncryptionDisabled           bool                     `json:"encryptionDisabled"`
	Flags                        *AccountFlags            `json:"flags"`
	Campaign                     *AccountCampaignSettings `json:"campaign"`
}

type Account struct {
	Id                  uuid.UUID        `gorm:"type:uuid;primary_key"`
	Name                string           `gorm:"type:string;not null"`
	Settings            *AccountSettings `gorm:"type:jsonb;serializer:json;not null;default:'{}'"`
	Data                datatypes.JSON   `gorm:"type:jsonb;serializer:json;not null;default:'{}'"`
	EncryptionKey       string           `gorm:"type:string;size:544;not null"`
	IsActive            bool             `gorm:"default:false"`
	IsCampaignActive    bool             `gorm:"default:false"`
	WizardProgress      string           `gorm:"type:string;default:'notStarted';not null"`
	Plan                *AccountPlan     `gorm:"type:jsonb;serializer:json;not null;default:'{}'"`
	AgnusSignatureKey   string           `gorm:"type:string"`
	ExpiresAt           *time.Time       `gorm:"type:timestamptz;default:null"`
	CorrelationId       string           `gorm:"type:string;default:''"`
	Branch              string           `gorm:"type:string;default:'default';not null"`
	DefaultDepartmentId uuid.UUID        `gorm:"type:uuid;default:null" json:"defaultDepartmentId,omitempty"`
	Users               []*User          `gorm:"foreignKey:AccountId"`
	Messages            []*Message       `gorm:"foreignKey:AccountId"`
	Services            []*Service       `gorm:"foreignKey:AccountId"`
	ClusterId           uuid.UUID        `gorm:"type:uuid;default:null" json:"clusterId,omitempty"`
	CreatedAt           *time.Time       `json:"createdAt,omitempty"`
	UpdatedAt           *time.Time       `json:"updatedAt,omitempty"`
	DeletedAt           *gorm.DeletedAt  `gorm:"index" json:"deletedAt,omitempty"`
	//Cluster                   Cluster                    `gorm:"foreignKey:ClusterId"`
	//DefaultDepartment         Department       `gorm:"foreignKey:DefaultDepartmentId"`
	//Roles                     []Role           `gorm:"foreignKey:AccountId"`
	//AcceptanceTerms           []AcceptanceTerm `gorm:"foreignKey:AccountId"`
	//Departments               []Department     `gorm:"foreignKey:AccountId"`
	//HolIdays                  []HolIday        `gorm:"foreignKey:AccountId"`
	//Distribution              []Distribution   `gorm:"foreignKey:AccountId"`
	//Integrations              []Integration              `gorm:"foreignKey:AccountId"`
	//ServicesWebhookFails      []ServicesWebhookFail      `gorm:"foreignKey:AccountId"`
	//ContactBlockLists         []ContactBlockList         `gorm:"foreignKey:AccountId"`
	//ContactBlockListItems     []ContactBlockListItem     `gorm:"foreignKey:AccountId"`
	//ContactBlockListsControls []ContactBlockListsControl `gorm:"foreignKey:AccountId"`
}

func (Account) TableName() string {
	return "accounts"
}

func (account *Account) BeforeCreate(tx *gorm.DB) (err error) {
	account.Id = uuid.New()
	return
}
