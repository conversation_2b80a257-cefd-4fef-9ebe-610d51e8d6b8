package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type TicketTransferAction string

const (
	Opened      TicketTransferAction = "opened"
	Transferred TicketTransferAction = "transferred"
	Closed      TicketTransferAction = "closed"
)

type TransferMetrics struct {
	WaitingTime      int  `json:"waitingTime,omitempty"`
	MessagingTime    int  `json:"messagingTime,omitempty"`
	TicketTime       int  `json:"ticketTime,omitempty"`
	TransferredByBot bool `json:"transferredByBot,omitempty"`
}

type TicketTransfer struct {
	Id                   uuid.UUID            `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	AccountId            uuid.UUID            `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	Action               TicketTransferAction `gorm:"type:enum_ticket_transfers_action" json:"action,omitempty"`
	Comments             string               `gorm:"type:text" json:"comments,omitempty"`
	Metrics              *TransferMetrics     `gorm:"type:jsonb;serializer:json;default:'{}'" json:"metrics,omitempty"`
	ToUserId             uuid.UUID            `gorm:"type:uuid;default:null" json:"toUserId,omitempty"`
	FromUserId           uuid.UUID            `gorm:"type:uuid;default:null" json:"fromUserId,omitempty"`
	FromDepartmentId     uuid.UUID            `gorm:"type:uuid;default:null" json:"fromDepartmentId,omitempty"`
	ToDepartmentId       uuid.UUID            `gorm:"type:uuid;default:null" json:"toDepartmentId,omitempty"`
	StartedAt            *time.Time           `gorm:"type:timestamptz;default:null" json:"startedAt,omitempty"`
	EndedAt              *time.Time           `gorm:"type:timestamptz;default:null" json:"endedAt,omitempty"`
	TransferredMessageId uuid.UUID            `gorm:"type:uuid;default:null" json:"transferredMessageId,omitempty"`
	FirstMessageId       uuid.UUID            `gorm:"type:uuid;default:null" json:"firstMessageId,omitempty"`
	LastMessageId        uuid.UUID            `gorm:"type:uuid;default:null" json:"lastMessageId,omitempty"`
	ByUserId             uuid.UUID            `gorm:"type:uuid;default:null" json:"byUserId,omitempty"`
	CreatedAt            *time.Time           `json:"createdAt,omitempty"`
	UpdatedAt            *time.Time           `json:"updatedAt,omitempty"`
	FromDistribution     bool                 `gorm:"default:false;not null" json:"fromDistribution,omitempty"`
	Account              *Account             `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	ToDepartment         *Department          `gorm:"foreignKey:ToDepartmentId" json:"toDepartment,omitempty"`
	FromDepartment       *Department          `gorm:"foreignKey:FromDepartmentId" json:"fromDepartment,omitempty"`
	ToUser               *User                `gorm:"foreignKey:ToUserId" json:"toUser,omitempty"`
	FromUser             *User                `gorm:"foreignKey:FromUserId" json:"fromUser,omitempty"`
	TransferredMessage   *Message             `gorm:"foreignKey:TransferredMessageId" json:"transferredMessage,omitempty"`
	FirstMessage         *Message             `gorm:"foreignKey:FirstMessageId" json:"firstMessage,omitempty"`
	LastMessage          *Message             `gorm:"foreignKey:LastMessageId" json:"lastMessage,omitempty"`
	ByUser               *User                `gorm:"foreignKey:ByUserId" json:"byUser,omitempty"`
	TicketId             uuid.UUID            `gorm:"type:uuid;default:null" json:"ticketId,omitempty"`
	Ticket               *Ticket              `gorm:"foreignKey:TicketId" json:"ticket,omitempty"`
}

func (TicketTransfer) CreateEnums(db *gorm.DB) error {
	return CreateEnumIfNotExists(db, "enum_ticket_transfers_action", []string{"opened", "transferred", "closed"})
}

func (TicketTransfer) TableName() string {
	return "ticket_transfers"
}

func (t *TicketTransfer) BeforeCreate(tx *gorm.DB) (err error) {
	t.Id = uuid.New()
	return
}
