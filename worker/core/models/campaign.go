package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type CampaignStatus string

const (
	CampaignStatusImportingContacts   CampaignStatus = "importing_contacts"
	CampaignStatusReady               CampaignStatus = "ready"
	CampaignStatusQueued              CampaignStatus = "queued"
	CampaignStatusProcessing          CampaignStatus = "processing"
	CampaignStatusPaused              CampaignStatus = "paused"
	CampaignStatusWaitingConnection   CampaignStatus = "waiting_connection"
	CampaignStatusCanceled            CampaignStatus = "canceled"
	CampaignStatusInsufficientCredits CampaignStatus = "insufficient_credits"
	CampaignStatusHsmLimitExceeded    CampaignStatus = "hsm_limit_exceeded"
	CampaignStatusImportError         CampaignStatus = "import_error"
	CampaignStatusError               CampaignStatus = "error"
	CampaignStatusDone                CampaignStatus = "done"
)

type CampaignConfig struct {
	MinInterval int    `json:"minInterval,omitempty"`
	MaxInterval int    `json:"maxInterval,omitempty"`
	NameField   string `json:"nameField,omitempty"`
	NumberField string `json:"numberField,omitempty"`
	Delimiter   string `json:"delimiter,omitempty"`
	Error       *struct {
		Message string `json:"message,omitempty"`
	} `json:"error,omitempty"`
}

type Campaign struct {
	Id                    uuid.UUID                  `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Title                 string                     `gorm:"type:text;not null" json:"title,omitempty"`
	Status                CampaignStatus             `gorm:"type:enum_campaigns_status;default:'ready'" json:"status,omitempty"`
	Config                *CampaignConfig            `gorm:"type:jsonb;serializer:json;default:'{}';" json:"config,omitempty"`
	IsScheduled           bool                       `gorm:"default:false;not null" json:"isScheduled,omitempty"`
	SendsAt               *time.Time                 `gorm:"type:timestamptz" json:"sendsAt,omitempty"`
	StartedAt             *time.Time                 `gorm:"type:timestamptz" json:"startedAt,omitempty"`
	FinishedAt            *time.Time                 `gorm:"type:timestamptz" json:"finishedAt,omitempty"`
	TotalMessagesCount    int                        `gorm:"type:int" json:"totalMessagesCount,omitempty"`
	SentMessagesCount     int                        `gorm:"type:int" json:"sentMessagesCount,omitempty"`
	TotalContacts         int                        `gorm:"type:int" json:"totalContacts,omitempty"`
	TotalContactsImported int                        `gorm:"type:int" json:"totalContactsImported,omitempty"`
	TotalValidContacts    int                        `gorm:"type:int" json:"totalValidContacts,omitempty"`
	MustOpenTicket        bool                       `gorm:"default:false" json:"mustOpenTicket,omitempty"`
	CreatedAt             *time.Time                 `json:"createdAt,omitempty"`
	UpdatedAt             *time.Time                 `json:"updatedAt,omitempty"`
	DeletedAt             *gorm.DeletedAt            `gorm:"index" json:"deletedAt,omitempty"`
	AccountId             uuid.UUID                  `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	ServiceId             uuid.UUID                  `gorm:"type:uuid;not null" json:"serviceId,omitempty"`
	DefaultDepartmentId   uuid.UUID                  `gorm:"type:uuid" json:"defaultDepartmentId,omitempty"`
	DefaultUserId         uuid.UUID                  `gorm:"type:uuid" json:"defaultUserId,omitempty"`
	CreatedById           uuid.UUID                  `gorm:"type:uuid" json:"createdById,omitempty"`
	SentById              uuid.UUID                  `gorm:"type:uuid" json:"sentById,omitempty"`
	Account               *Account                   `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Service               *Service                   `gorm:"foreignKey:ServiceId" json:"service,omitempty"`
	DefaultDepartment     *Department                `gorm:"foreignKey:DefaultDepartmentId" json:"defaultDepartment,omitempty"`
	DefaultUser           *User                      `gorm:"foreignKey:DefaultUserId" json:"defaultUser,omitempty"`
	CreatedBy             *User                      `gorm:"foreignKey:CreatedById" json:"createdBy,omitempty"`
	SentBy                *User                      `gorm:"foreignKey:SentById" json:"sentBy,omitempty"`
	Tags                  []*Tag                     `gorm:"many2many:campaign_contact_lists;foreignKey:Id;joinForeignKey:CampaignId;References:Id;joinReferences:TagId" json:"tags,omitempty"`
	Messages              []*CampaignMessage         `gorm:"foreignKey:CampaignId" json:"messages,omitempty"`
	Progress              []*CampaignMessageProgress `gorm:"foreignKey:CampaignId;as:campaignMessagesProgress" json:"progress,omitempty"`
	ImportFile            *File                      `gorm:"foreignKey:AttachedId;scope:AttachedType=campaign.file;constraint:false" json:"importFile,omitempty"`
}

func (Campaign) CreateEnums(db *gorm.DB) error {
	return CreateEnumIfNotExists(db, "enum_campaigns_status", []string{"ready", "queued", "processing", "paused", "waiting_connection", "canceled", "insufficient_credits", "hsm_limit_exceeded", "import_error", "error", "done"})
}

func (Campaign) TableName() string {
	return "campaigns"
}

func (c *Campaign) BeforeCreate(tx *gorm.DB) (err error) {
	c.Id = uuid.New()
	return
}
