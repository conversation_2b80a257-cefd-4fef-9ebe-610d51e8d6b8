package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type WhatsappBusinessButtonTypeEnum string

const (
	WhatsappBusinessButtonTypeURL         WhatsappBusinessButtonTypeEnum = "URL"
	WhatsappBusinessButtonTypePhoneNumber WhatsappBusinessButtonTypeEnum = "PHONE_NUMBER"
	WhatsappBusinessButtonTypeQuickReply  WhatsappBusinessButtonTypeEnum = "QUICK_REPLY"
	WhatsappBusinessButtonTypeOTP         WhatsappBusinessButtonTypeEnum = "OTP"
)

type WhatsappBusinessButtonOtpTypeEnum string

const (
	WhatsappBusinessButtonOtpTypeCopyCode WhatsappBusinessButtonOtpTypeEnum = "COPY_CODE"
	WhatsappBusinessButtonOtpTypeOneTap   WhatsappBusinessButtonOtpTypeEnum = "ONE_TAP"
	WhatsappBusinessButtonOtpTypeZeroTap  WhatsappBusinessButtonOtpTypeEnum = "ZERO_TAP"
)

// WhatsappBusinessComponentParameterButton
// @jsonb
type WhatsappBusinessComponentParameterButton struct {
	Type          WhatsappBusinessButtonTypeEnum    `json:"type"`
	Text          string                            `json:"text,omitempty"`
	PhoneNumber   string                            `json:"phone_number,omitempty"`
	URL           string                            `json:"url,omitempty"`
	Example       []string                          `json:"example,omitempty"`
	OtpType       WhatsappBusinessButtonOtpTypeEnum `json:"otp_type,omitempty"`
	AutofillText  string                            `json:"autofill_text,omitempty"`  // Para ONE_TAP
	PackageName   string                            `json:"package_name,omitempty"`   // Para ONE_TAP
	SignatureHash string                            `json:"signature_hash,omitempty"` // Para ONE_TAP
}

// WhatsappBusinessComponentExample
// @jsonb
type WhatsappBusinessComponentExample struct {
	HeaderHandle []string   `json:"header_handle"`
	HeaderText   []string   `json:"header_text"`
	BodyText     [][]string `json:"body_text"`
}

type OTPType string

const (
	OTPTypeCopyCode OTPType = "COPY_CODE"
	OTPTypeOneTap   OTPType = "ONE_TAP"
)

// WhatsappBusinessComponent
// @jsonb
type WhatsappBusinessComponent struct {
	Type                        string                                      `json:"type,omitempty"`
	Format                      string                                      `json:"format,omitempty"` // IMAGE, VIDEO, AUDIO, DOCUMENT
	Text                        string                                      `json:"text,omitempty"`
	Buttons                     []*WhatsappBusinessComponentParameterButton `json:"buttons,omitempty"`
	Example                     *WhatsappBusinessComponentExample           `json:"example,omitempty"`
	Params                      []string                                    `json:"params,omitempty"`
	AddSecurityRecommendation   bool                                        `json:"add_security_recommendation,omitempty"`
	CodeExpirationMinutes       int                                         `json:"code_expiration_minutes,omitempty"`
	ActiveCodeExpirationMinutes bool                                        `json:"active_code_expiration_minutes,omitempty"`
	OtpType                     OTPType                                     `json:"otp_type,omitempty"`
}

type WhatsappBusinessTemplateQualityEnum string

const (
	WhatsappBusinessTemplateQualityHigh    WhatsappBusinessTemplateQualityEnum = "HIGH"
	WhatsappBusinessTemplateQualityMedium  WhatsappBusinessTemplateQualityEnum = "MEDIUM"
	WhatsappBusinessTemplateQualityLow     WhatsappBusinessTemplateQualityEnum = "LOW"
	WhatsappBusinessTemplateQualityUnknown WhatsappBusinessTemplateQualityEnum = "UNKNOWN"
)

type WhatsappBusinessTemplateStatusEnum string

const (
	WhatsappBusinessTemplateStatusError    WhatsappBusinessTemplateStatusEnum = "ERROR"
	WhatsappBusinessTemplateStatusApproved WhatsappBusinessTemplateStatusEnum = "APPROVED"
	WhatsappBusinessTemplateStatusPending  WhatsappBusinessTemplateStatusEnum = "PENDING"
	WhatsappBusinessTemplateStatusRejected WhatsappBusinessTemplateStatusEnum = "REJECTED"
	WhatsappBusinessTemplateStatusFailed   WhatsappBusinessTemplateStatusEnum = "FAILED"
	WhatsappBusinessTemplateStatusSending  WhatsappBusinessTemplateStatusEnum = "SENDING"
	WhatsappBusinessTemplateStatusEmpty    WhatsappBusinessTemplateStatusEnum = ""
)

type WhatsappBusinessMessageTypeEnum string

const (
	WhatsappBusinessMessageTypeTextOnly    WhatsappBusinessMessageTypeEnum = "text_only"
	WhatsappBusinessMessageTypeInteractive WhatsappBusinessMessageTypeEnum = "interactive"
)

type WhatsappBusinessTemplate struct {
	Id             uuid.UUID                           `gorm:"type:uuid;primaryKey" json:"id"`
	InternalName   string                              `gorm:"type:string" json:"internalName,omitempty"`
	Category       string                              `gorm:"type:string;not null" json:"category"`
	Components     []*WhatsappBusinessComponent        `gorm:"type:jsonb;serializer:json;default:'{}'" json:"components,omitempty"`
	Language       string                              `gorm:"type:string;not null" json:"language"`
	Name           string                              `gorm:"type:string;not null;uniqueIndex:idx_name_lang_service_account" json:"name"`
	Namespace      string                              `gorm:"type:string;not null" json:"namespace"`
	RejectedReason string                              `gorm:"type:string" json:"rejectedReason,omitempty"`
	Status         WhatsappBusinessTemplateStatusEnum  `gorm:"type:string;not null" json:"status"`
	MessageType    WhatsappBusinessMessageTypeEnum     `gorm:"type:string;not null" json:"messageType"`
	ServiceId      uuid.UUID                           `gorm:"type:uuid;not null;uniqueIndex:idx_name_lang_service_account" json:"serviceId"`
	Service        *Service                            `gorm:"foreignKey:ServiceId" json:"service,omitempty"`
	AccountId      uuid.UUID                           `gorm:"type:uuid;not null;uniqueIndex:idx_name_lang_service_account" json:"accountId"`
	Account        *Account                            `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	IdGupshup      string                              `gorm:"type:string" json:"idGupshup,omitempty"`
	Quality        WhatsappBusinessTemplateQualityEnum `gorm:"type:string" json:"quality,omitempty"`
	ArchivedAt     *time.Time                          `json:"archivedAt,omitempty"`
	CreatedAt      *time.Time                          `json:"createdAt"`
	UpdatedAt      *time.Time                          `json:"updatedAt"`
	DeletedAt      gorm.DeletedAt                      `gorm:"index" json:"deletedAt,omitempty"`
	FileExample    []*File                             `gorm:"foreignKey:AttachedId" json:"file,omitempty"`
}

func (WhatsappBusinessTemplate) TableName() string {
	return "whatsapp_business_templates"
}

func (w *WhatsappBusinessTemplate) BeforeCreate(tx *gorm.DB) (err error) {
	w.Id = uuid.New()
	return
}
