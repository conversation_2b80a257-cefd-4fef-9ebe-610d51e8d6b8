package adapter_types

import (
	"context"
	"digisac-go/worker/core/models"
	"io"
	"time"

	"github.com/google/uuid"
)

type MessageTypeEnum string

const (
	MESSAGE_TYPE_TEXT               MessageTypeEnum = "chat"
	MESSAGE_TYPE_STICKER            MessageTypeEnum = "sticker"
	MESSAGE_TYPE_DOCUMENT           MessageTypeEnum = "document"
	MESSAGE_TYPE_VOICE              MessageTypeEnum = "audio"
	MESSAGE_TYPE_GROUP_CHAT_CREATED MessageTypeEnum = "group_chat_created"
	MESSAGE_TYPE_IMAGE              MessageTypeEnum = "image"
	MESSAGE_TYPE_VIDEO              MessageTypeEnum = "video"
	MESSAGE_TYPE_REACTION           MessageTypeEnum = "reaction"
	MESSAGE_TYPE_ANIMATION          MessageTypeEnum = "video"
	MESSAGE_TYPE_LOCATION           MessageTypeEnum = "location"
	MESSAGE_TYPE_VCARD              MessageTypeEnum = "vcard"
	MESSAGE_TYPE_E2E_NOTIFICATION   MessageTypeEnum = "e2e_notification"
)

const CONTACT_TYPE_PRIVATE = "private"
const CONTACT_TYPE_GROUP = "group"
const CONTACT_TYPE_SUPER_GROUP = "supergroup"
const CONTACT_TYPE_CHANNEL = "channel"

type SendReactionPayload struct {
	To        string // contact.idFromService
	MessageId string // message.idFromService
	Reaction  string
}

type RevokeReactionPayload struct {
	To        string // contact.idFromService
	MessageId string // message.idFromService
}

type SendTextPayload struct {
	To             string // contact.idFromService
	ReplyMessageId string // message.idFromService
	Text           string
}

type SendAudioPayload struct {
	To             string // contact.idFromService
	ReplyMessageId string // message.idFromService
	Caption        string
	Mimetype       string
	Url            string
}

type SendImagePayload struct {
	To             string // contact.idFromService
	ReplyMessageId string // message.idFromService
	Caption        string
	Mimetype       string
	Filename       string // file.name
	Url            string
}

type SendVideoPayload struct {
	To             string // contact.idFromService
	ReplyMessageId string // message.idFromService
	Caption        string
	Mimetype       string
	Filename       string // file.name
	Url            string
}

type SendDocumentPayload struct {
	To             string // contact.idFromService
	ReplyMessageId string // message.idFromService
	Caption        string
	Mimetype       string
	Filename       string // file.name
	Url            string
}

type SendTemplatePayload struct {
	To             string // contact.idFromService
	ReplyMessageId string // message.idFromService
	Template       *models.WhatsappBusinessTemplate
	Url            string
	Parameters     []*models.HsmParameters
}

// SendInteractiveMessagePayload represents the payload for sending an interactive message
type SendInteractiveMessagePayload struct {
	To             string // contact.idFromService
	ReplyMessageId string // message.idFromService
	Interactive    *models.InteractiveMessage
	Url            string
}

type SendStickerPayload struct {
	To             string // contact.idFromService
	ReplyMessageId string // message.idFromService
	Url            string
}

type SendMessageResponse struct {
	MessageId string
	Timestamp *time.Time
	Ack       string
	Error     *models.MessageError
}

type WebhookPayload[T any] struct {
	Payload T
}

type WebhookResponse struct {
}

type WebhookContact struct {
	Id              string `json:"id,omitempty"`
	Name            string `json:"name,omitempty"`
	AlternativeName string `json:"alternativeName,omitempty"`
	IsGroup         bool   `json:"isGroup,omitempty"`
	IsMe            bool   `json:"isMe,omitempty"`
	Visible         bool   `json:"visible,omitempty"`
	AvatarUrl       string `json:"avatarUrl,omitempty"`
}

type Reaction struct {
	Emoji string `json:"emoji,omitempty"`
}

type WebhookReaction struct {
	Timestamp    *time.Time  `json:"timestamp,omitempty"`
	NewReactions []*Reaction `json:"newReactions,omitempty"`
	IsFromMe     bool        `json:"isFromMe,omitempty"`
}

type WebhookService struct {
	Data *models.ServiceData `json:"data,omitempty"` // Usado para atualizar a conexão
}

type WebhookMessageFile struct {
	Mimetype string `json:"mimetype,omitempty"`
	Id       string `json:"id,omitempty"`
	Url      string `json:"url,omitempty"`
	Name     string `json:"name,omitempty"`
}

type WebhookMessageLocation struct {
	Lat        float64 `json:"lat,omitempty"`
	Lng        float64 `json:"lng,omitempty"`
	PreviewUrl string  `json:"previewUrl,omitempty"`
}

type CtwaContext struct {
	ConversionSource string `json:"conversionSource,omitempty"`
	Description      string `json:"description,omitempty"`
	IsSuspiciousLink string `json:"isSuspiciousLink,omitempty"`
	MediaType        int32  `json:"mediaType,omitempty"`
	MediaUrl         string `json:"mediaUrl,omitempty"`
	SourceUrl        string `json:"sourceUrl,omitempty"`
	ThumbnailUrl     string `json:"thumbnailUrl,omitempty"`
	Title            string `json:"title,omitempty"`
}

type WebhookMessage struct {
	Id                  string                  `json:"id,omitempty"`
	Type                string                  `json:"type,omitempty"`
	Text                string                  `json:"text,omitempty"`
	Timestamp           *time.Time              `json:"timestamp,omitempty"`
	ReplyMessageId      string                  `json:"replyMessageId,omitempty"`
	ReplyMessageWebhook *BuiltWebhook           `json:"replyMessageWebhook,omitempty"`
	File                *WebhookMessageFile     `json:"file,omitempty"`
	IsFromMe            bool                    `json:"isFromMe,omitempty"`
	Location            *WebhookMessageLocation `json:"location,omitempty"`
	Vcard               string                  `json:"vcard,omitempty"`
	CtwaContext         *CtwaContext            `json:"ctwaContext,omitempty"`
}

type WebhookStatuses struct {
	Error     *models.MessageError `json:"error,omitempty"`
	Ack       string               `json:"ack,omitempty"`
	Id        string               `json:"id,omitempty"`
	Timestamp *time.Time           `json:"timestamp,omitempty"`
}

type WebhookTemplateType string

const (
	WebhookTemplateTypeStatus  WebhookTemplateType = "template_status"
	WebhookTemplateTypeQuality WebhookTemplateType = "template_quality"
)

type WebhookTemplate struct {
	Id             string              `json:"id,omitempty"`
	Status         string              `json:"status,omitempty"`
	Quality        string              `json:"quality,omitempty"`
	RejectedReason string              `json:"rejectedReason,omitempty"`
	Type           WebhookTemplateType `json:"type,omitempty"`
}

type BuiltWebhook struct {
	Contact  *WebhookContact  `json:"contact,omitempty"`
	From     *WebhookContact  `json:"from,omitempty"`
	Message  *WebhookMessage  `json:"message,omitempty"`
	Statuses *WebhookStatuses `json:"statuses,omitempty"`
	Reaction *WebhookReaction `json:"reaction,omitempty"`
	Service  *WebhookService  `json:"service,omitempty"`
	Template *WebhookTemplate `json:"template,omitempty"`
}

type DownloadMediaResponse struct {
	Type   string // stream, buffer, base64
	Base64 string
	Buffer []byte
	Stream io.Reader
}

type LoadEarlierMessagesPayload struct {
	IdFromService string
	Timestamp     *time.Time
}

type LoadEarlierMessagesResponse struct {
	Messages []*models.Message
}

type ForwardMessagePayload struct {
	MessageId string
	ToId      string
	// ContactId        string // partial_id?
}

type ForwardMessageResponse struct {
}

type AdapterInterface interface {
	Start(ctx context.Context, serviceId uuid.UUID) error    // Configura a conexão com os dados do internalData, inicia instancia do client
	Shutdown(ctx context.Context, serviceId uuid.UUID) error // Desliga a conexão, desliga o cliente e remove a instancia do client
	Logout(ctx context.Context, serviceId uuid.UUID) error   // "Logout" vai invalidar os acessos, por exemplo o token
	Takeover(ctx context.Context, serviceId uuid.UUID) error // "Usar aqui" na conexão, apenas para whatsapp

	SetWebhook(ctx context.Context, serviceId uuid.UUID, url string) error // configura webhook para conexão
	Refresh(ctx context.Context, serviceId uuid.UUID) error                // reconfigura client da conexão //(ex: recria webhook, gateway, webchat, imap, smtp)

	NewToken(ctx context.Context, serviceId uuid.UUID) error // gera novo token no broker, apenas para positus

	SendSticker(ctx context.Context, serviceId uuid.UUID, payload *SendStickerPayload) (*SendMessageResponse, error)
	RevokeReaction(ctx context.Context, serviceId uuid.UUID, payload *RevokeReactionPayload) (bool, error)
	SendReaction(ctx context.Context, serviceId uuid.UUID, payload *SendReactionPayload) (bool, error)
	SendText(ctx context.Context, serviceId uuid.UUID, payload *SendTextPayload) (*SendMessageResponse, error)
	SendAudio(ctx context.Context, serviceId uuid.UUID, payload *SendAudioPayload) (*SendMessageResponse, error)
	SendImage(ctx context.Context, serviceId uuid.UUID, payload *SendImagePayload) (*SendMessageResponse, error)
	SendVideo(ctx context.Context, serviceId uuid.UUID, payload *SendVideoPayload) (*SendMessageResponse, error)
	SendDocument(ctx context.Context, serviceId uuid.UUID, payload *SendDocumentPayload) (*SendMessageResponse, error)
	SendTemplate(ctx context.Context, serviceId uuid.UUID, payload *SendTemplatePayload) (*SendMessageResponse, error)
	SendInteractiveMessage(ctx context.Context, serviceId uuid.UUID, payload *SendInteractiveMessagePayload) (*SendMessageResponse, error)
	BuildWebhook(ctx context.Context, serviceId uuid.UUID, payload any) ([]*BuiltWebhook, error)                             // Mapeia o payload para struct e faz o build para BuiltWebhook
	DownloadMedia(ctx context.Context, serviceId uuid.UUID, mediaId string, mediaUrl string) (*DownloadMediaResponse, error) // Baixa mídia do driver
	LoadEarlierMessages(ctx context.Context, serviceId uuid.UUID, payload *LoadEarlierMessagesPayload) ([]*BuiltWebhook, error)
	ForwardMessage(ctx context.Context, serviceId uuid.UUID, payload *ForwardMessagePayload) (*ForwardMessageResponse, error)

	GetTemplates(ctx context.Context, serviceId uuid.UUID) ([]*models.WhatsappBusinessTemplate, error)                       // Pega templates waba
	CreateTemplate(ctx context.Context, serviceId uuid.UUID, templateId uuid.UUID) (*models.WhatsappBusinessTemplate, error) // Cria template waba
	DeleteTemplate(ctx context.Context, serviceId uuid.UUID, templateId string) error                                        // Deleta template waba

}
