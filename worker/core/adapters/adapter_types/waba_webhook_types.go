package adapter_types

// Tipos para webhooks do WhatsApp Business API

type WhatsappBusinessWebhookMedia struct {
	Id       string `json:"id"`
	MimeType string `json:"mime_type"`
	Sha256   string `json:"sha256"`
	URL      string `json:"url"`
	Caption  string `json:"caption"`
	FileName string `json:"filename"`
}

type WhatsappBusinessWebhookSticker struct {
	WhatsappBusinessWebhookMedia
	Animated bool `json:"animated"`
}

type WhatsappBusinessWebhookContactPhone struct {
	Phone string `json:"phone"`
	Type  string `json:"type"`
	WaId  string `json:"wa_id"`
}

type WhatsappBusinessWebhookContactName struct {
	FirstName     string `json:"first_name"`
	FormattedName string `json:"formatted_name"`
	LastName      string `json:"last_name"`
}

type WhatsappBusinessWebhookContactVcard struct {
	Name   *WhatsappBusinessWebhookContactName    `json:"name"`
	Phones []*WhatsappBusinessWebhookContactPhone `json:"phones"`
}

type WhatsappBusinessWebhookReaction struct {
	Emoji     string `json:"emoji"`
	MessageId string `json:"message_id"`
}

type WhatsappBusinessWebhookInteractive struct {
	Type      string `json:"type"` // list_reply, button_reply
	ListReply *struct {
		Id    string `json:"id"`
		Title string `json:"title"`
	} `json:"list_reply,omitempty"`
	ButtonReply *struct {
		Id    string `json:"id"`
		Title string `json:"title"`
	} `json:"button_reply,omitempty"`
}

type WhatsappBusinessWebhookMessage struct {
	Id        string `json:"id"`
	Type      string `json:"type"`
	Timestamp string `json:"timestamp"`
	Text      *struct {
		Body string `json:"body"`
	} `json:"text,omitempty"`
	Image       *WhatsappBusinessWebhookMedia          `json:"image,omitempty"`
	Video       *WhatsappBusinessWebhookMedia          `json:"video,omitempty"`
	Document    *WhatsappBusinessWebhookMedia          `json:"document,omitempty"`
	Audio       *WhatsappBusinessWebhookMedia          `json:"audio,omitempty"`
	Sticker     *WhatsappBusinessWebhookSticker        `json:"sticker,omitempty"`
	Contacts    []*WhatsappBusinessWebhookContactVcard `json:"contacts,omitempty"`
	Reaction    *WhatsappBusinessWebhookReaction       `json:"reaction,omitempty"`
	Interactive *WhatsappBusinessWebhookInteractive    `json:"interactive,omitempty"`
	Button      *struct {
		Payload string `json:"payload"`
		Text    string `json:"text"`
	} `json:"button,omitempty"`
	Location *struct {
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
		Name      string  `json:"name"`
		URL       string  `json:"url"`
	} `json:"location,omitempty"`
	Context *struct {
		GsId string `json:"gs_id"` // Usado no gupshup
		Id   string `json:"id"`
		From string `json:"from"`
	} `json:"context,omitempty"`
}

type WhatsappBusinessWebhookContact struct {
	WaId    string `json:"wa_id"`
	Profile struct {
		Name string `json:"name"`
	} `json:"profile"`
}

type WhatsappBusinessWebhookStatusError struct {
	Code      int `json:"code"`
	ErrorData *struct {
		Details string `json:"details"`
	} `json:"error_data"`
	Message string `json:"message"`
	Title   string `json:"title"`
}

type WhatsappBusinessWebhookStatus struct {
	Type        string                                `json:"type"`
	Id          string                                `json:"id"`
	GsId        string                                `json:"gs_id"`
	MetaMsgId   string                                `json:"meta_msg_id"`
	RecipientId string                                `json:"recipient_id"`
	Status      string                                `json:"status"`
	Timestamp   any                                   `json:"timestamp"`
	Errors      []*WhatsappBusinessWebhookStatusError `json:"errors"`
}

type WhatsappBusinessWebhookTemplate struct {
	Id        string `json:"id"`
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
	Language  struct {
		Policy string `json:"policy"`
		Code   string `json:"code"`
	} `json:"language"`
}

type WhatsappBusinessWebhookChange struct {
	Field string `json:"field"`
	Value struct {
		Messages  []*WhatsappBusinessWebhookMessage  `json:"messages,omitempty"`
		Contacts  []*WhatsappBusinessWebhookContact  `json:"contacts,omitempty"`
		Statuses  []*WhatsappBusinessWebhookStatus   `json:"statuses,omitempty"`
		Templates []*WhatsappBusinessWebhookTemplate `json:"templates,omitempty"`
	} `json:"value"`
}

type WhatsappBusinessWebhookEntry struct {
	Id      string                           `json:"id"`
	Time    string                           `json:"time"`
	Changes []*WhatsappBusinessWebhookChange `json:"changes,omitempty"`
}

type WhatsappBusinessWebhookPayload struct {
	AccountId string `json:"accountId"`
	ServiceId string `json:"serviceId"`
	Payload   struct {
		Entry []*WhatsappBusinessWebhookEntry `json:"entry,omitempty"`
	} `json:"payload"`
}
