//go:build unit
// +build unit

package gupshup_adapter

import (
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestExtractTemplateComponents(t *testing.T) {
	tests := []struct {
		name     string
		template *models.WhatsappBusinessTemplate
		want     *templateComponents
	}{
		{
			name: "template with all components",
			template: &models.WhatsappBusinessTemplate{
				Components: []*models.WhatsappBusinessComponent{
					{
						Type: adapter_types.ComponentTypeHeader,
						Text: "Header Text",
					},
					{
						Type: adapter_types.ComponentTypeBody,
						Text: "Body Text",
					},
					{
						Type: adapter_types.ComponentTypeFooter,
						Text: "Footer Text",
					},
					{
						Type: adapter_types.ComponentTypeButtons,
						Buttons: []*models.WhatsappBusinessComponentParameterButton{
							{
								Type: "URL",
								Text: "Click Here",
								URL:  "https://example.com",
							},
						},
					},
				},
			},
			want: &templateComponents{
				header: &models.WhatsappBusinessComponent{
					Type: adapter_types.ComponentTypeHeader,
					Text: "Header Text",
				},
				body: &models.WhatsappBusinessComponent{
					Type: adapter_types.ComponentTypeBody,
					Text: "Body Text",
				},
				footer: &models.WhatsappBusinessComponent{
					Type: adapter_types.ComponentTypeFooter,
					Text: "Footer Text",
				},
				buttons: []*models.WhatsappBusinessComponentParameterButton{
					{
						Type: "URL",
						Text: "Click Here",
						URL:  "https://example.com",
					},
				},
			},
		},
		{
			name: "template with only required components",
			template: &models.WhatsappBusinessTemplate{
				Components: []*models.WhatsappBusinessComponent{
					{
						Type: adapter_types.ComponentTypeBody,
						Text: "Body Text",
					},
				},
			},
			want: &templateComponents{
				body: &models.WhatsappBusinessComponent{
					Type: adapter_types.ComponentTypeBody,
					Text: "Body Text",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := extractTemplateComponents(tt.template)

			if tt.want.header != nil {
				assert.Equal(t, tt.want.header.Text, got.header.Text)
			}
			if tt.want.body != nil {
				assert.Equal(t, tt.want.body.Text, got.body.Text)
			}
			if tt.want.footer != nil {
				assert.Equal(t, tt.want.footer.Text, got.footer.Text)
			}
			if len(tt.want.buttons) > 0 {
				assert.Equal(t, len(tt.want.buttons), len(got.buttons))
				assert.Equal(t, tt.want.buttons[0].Type, got.buttons[0].Type)
				assert.Equal(t, tt.want.buttons[0].Text, got.buttons[0].Text)
				assert.Equal(t, tt.want.buttons[0].URL, got.buttons[0].URL)
			}
		})
	}
}
