//go:build unit
// +build unit

package base_waba_adapter

import (
	"context"
	"digisac-go/worker/core/models"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   string
		expected models.WhatsappBusinessTemplateStatusEnum
	}{
		{
			name:     "approved status",
			status:   "APPROVED",
			expected: models.WhatsappBusinessTemplateStatusApproved,
		},
		{
			name:     "pending status",
			status:   "PENDING",
			expected: models.WhatsappBusinessTemplateStatusPending,
		},
		{
			name:     "rejected status",
			status:   "REJECTED",
			expected: models.WhatsappBusinessTemplateStatusRejected,
		},
		{
			name:     "unknown status",
			status:   "UNKNOWN",
			expected: models.WhatsappBusinessTemplateStatusEmpty,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ParseStatus(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestProcessAuthenticationTemplate(t *testing.T) {
	adapter := &BaseWabaAdapter{}

	template := &models.WhatsappBusinessTemplate{
		Language: "pt_BR",
		Category: "AUTHENTICATION",
	}

	var components []*models.WhatsappBusinessComponent

	components = adapter.ProcessAuthenticationTemplate(context.Background(), template, components)

	assert.Len(t, components, 2)
	assert.Contains(t, components[0].Text, "código de verificação")
	assert.Contains(t, components[1].Text, "Este código expira em")
}
