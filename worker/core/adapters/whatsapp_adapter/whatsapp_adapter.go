package whatsapp_adapter

import (
	"bytes"
	"context"
	pb "digisac-go/common/grpc/api"
	pb_common "digisac-go/common/grpc/common"
	"digisac-go/worker/config"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/utils/common"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"log/slog"

	"github.com/google/uuid"
)

func NewWhatsappAdapter(serviceRepository repositories.ServiceRepository, config *config.Config, grpcWppClient pb.PublicServiceClient) *WhatsappAdapter {
	return &WhatsappAdapter{
		serviceRepository: serviceRepository,
		grpcWppClient:     grpcWppClient,
		config:            config,
	}
}

func (a *WhatsappAdapter) SetWebhook(ctx context.Context, serviceId uuid.UUID, url string) (err error) {
	return nil
}
func (a *WhatsappAdapter) Refresh(ctx context.Context, serviceId uuid.UUID) error  { return nil }
func (a *WhatsappAdapter) NewToken(ctx context.Context, serviceId uuid.UUID) error { return nil }
func (a *WhatsappAdapter) Takeover(ctx context.Context, serviceId uuid.UUID) error { return nil }

func (a *WhatsappAdapter) Logout(ctx context.Context, serviceId uuid.UUID) (err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in Logout", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("failed to find service: %w", err)
	}

	_, err = a.grpcWppClient.Logout(ctx, &pb_common.ActionPayload{
		ServiceId: service.Data.DriverId,
	})
	if err != nil {
		slog.ErrorContext(ctx, "Failed to logout whatsapp", slog.String("serviceId", serviceId.String()), slog.String("driverId", service.Data.DriverId), slog.Any("error", err))
		return fmt.Errorf("failed to logout whatsapp: %w", err)
	}

	return nil
}

func (a *WhatsappAdapter) Shutdown(ctx context.Context, serviceId uuid.UUID) (err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in Shutdown", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("failed to find service: %w", err)
	}

	_, err = a.grpcWppClient.Stop(ctx, &pb_common.ActionPayload{
		ServiceId: service.Data.DriverId,
	})
	if err != nil {
		slog.ErrorContext(ctx, "Failed to stop whatsapp", slog.String("serviceId", serviceId.String()), slog.String("driverId", service.Data.DriverId), slog.Any("error", err))
		return fmt.Errorf("failed to stop whatsapp: %w", err)
	}

	return nil
}

func (a *WhatsappAdapter) Start(ctx context.Context, serviceId uuid.UUID) error {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "service not found in Start", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("service not found: %w", err)
	}

	var driverId string
	driverId = service.Data.DriverId

	if driverId != "" {
		whatsappService, err := a.grpcWppClient.GetService(ctx, &pb_common.GetServiceRequest{
			Id: driverId,
		})
		if err != nil {
			slog.ErrorContext(ctx, "Failed to get whatsapp service", slog.String("driverId", driverId), slog.Any("error", err))
			return fmt.Errorf("failed to get whatsapp service: %w", err)
		}
		driverId = whatsappService.Service.Id
	}

	if driverId == "" {
		createdWhatsappService, err := a.grpcWppClient.CreateService(ctx, &pb_common.CreateServiceRequest{
			Name: service.Name,
		})
		if err != nil {
			slog.ErrorContext(ctx, "Failed to create whatsapp service", slog.String("serviceName", service.Name), slog.Any("error", err))
			return fmt.Errorf("failed to create whatsapp service: %w", err)
		}
		driverId = createdWhatsappService.Id
	}

	service.Data.DriverId = driverId

	err = a.serviceRepository.UpdateById(ctx, service.Id, service)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update service with driverId", slog.String("serviceId", serviceId.String()), slog.String("driverId", driverId), slog.Any("error", err))
		return fmt.Errorf("failed to update service with driverId: %w", err)
	}

	_, err = a.grpcWppClient.Start(ctx, &pb_common.ActionPayload{
		ServiceId: driverId,
	})
	if err != nil {
		slog.ErrorContext(ctx, "Failed to start service via gRPC", slog.String("serviceId", serviceId.String()), slog.String("driverId", driverId), slog.Any("error", err))
		return fmt.Errorf("failed to start service via gRPC: %w", err)
	}

	return nil
}

func (a *WhatsappAdapter) SendReaction(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendReactionPayload) (response bool, err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in SendReaction", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return false, fmt.Errorf("failed to find service: %w", err)
	}

	_, err = a.grpcWppClient.SendReactionToMessage(ctx, &pb_common.SendReactionToMessageRequest{
		Payload: &pb_common.SendReactionToMessagePayload{
			ChatId:    payload.To,
			MessageId: payload.MessageId,
			Reaction:  payload.Reaction,
		},
		Metadata: &pb_common.Metadata{
			ServiceId: service.Data.DriverId,
		},
	})
	if err != nil {
		slog.ErrorContext(ctx, "Failed to send reaction to message", slog.String("serviceId", serviceId.String()), slog.String("chatId", payload.To), slog.String("messageId", payload.MessageId), slog.Any("error", err))
		return false, fmt.Errorf("failed to send reaction to message: %w", err)
	}

	return true, nil
}

func (a *WhatsappAdapter) RevokeReaction(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.RevokeReactionPayload) (response bool, err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in RevokeReaction", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return false, fmt.Errorf("failed to find service: %w", err)
	}

	_, err = a.grpcWppClient.SendReactionToMessage(ctx, &pb_common.SendReactionToMessageRequest{
		Payload: &pb_common.SendReactionToMessagePayload{
			ChatId:    payload.To,
			MessageId: payload.MessageId,
			Reaction:  "",
		},
		Metadata: &pb_common.Metadata{
			ServiceId: service.Data.DriverId,
		},
	})
	if err != nil {
		slog.ErrorContext(ctx, "Failed to revoke reaction from message", slog.String("serviceId", serviceId.String()), slog.String("chatId", payload.To), slog.String("messageId", payload.MessageId), slog.Any("error", err))
		return false, fmt.Errorf("failed to revoke reaction from message: %w", err)
	}

	return true, nil
}

func (a *WhatsappAdapter) SendText(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendTextPayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in SendText", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	response, err := a.grpcWppClient.SendMessage(ctx,
		&pb_common.SendMessageRequest{
			Payload: &pb_common.SendMessagePayload{
				ChatId:          payload.To,
				Text:            payload.Text,
				QuotedMessageId: payload.ReplyMessageId,
			},
			Metadata: &pb_common.Metadata{
				ServiceId: service.Data.DriverId,
			},
		},
	)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to send text message", slog.String("serviceId", serviceId.String()), slog.String("chatId", payload.To), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Now()

	return &adapter_types.SendMessageResponse{
		MessageId: response.Id,
		Timestamp: &timestamp,
		Ack:       "0",
	}, nil
}

func (a *WhatsappAdapter) SendAudio(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendAudioPayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in SendAudio", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	response, err := a.grpcWppClient.SendMessage(ctx,
		&pb_common.SendMessageRequest{
			Payload: &pb_common.SendMessagePayload{
				ChatId:          payload.To,
				Text:            payload.Caption,
				QuotedMessageId: payload.ReplyMessageId,
				File: &pb_common.File{
					Url:      payload.Url,
					Mimetype: payload.Mimetype,
					AsPtt:    true,
				},
			},
			Metadata: &pb_common.Metadata{
				ServiceId: service.Data.DriverId,
			},
		},
	)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to send audio message", slog.String("serviceId", serviceId.String()), slog.String("chatId", payload.To), slog.String("url", payload.Url), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Now()

	return &adapter_types.SendMessageResponse{
		MessageId: response.Id,
		Timestamp: &timestamp,
		Ack:       "0",
	}, nil
}

func (a *WhatsappAdapter) SendImage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendImagePayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in SendImage", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	response, err := a.grpcWppClient.SendMessage(ctx,
		&pb_common.SendMessageRequest{
			Payload: &pb_common.SendMessagePayload{
				ChatId:          payload.To,
				Text:            payload.Caption,
				QuotedMessageId: payload.ReplyMessageId,
				File: &pb_common.File{
					Url:      payload.Url,
					Mimetype: payload.Mimetype,
					Name:     payload.Filename,
				},
			},
			Metadata: &pb_common.Metadata{
				ServiceId: service.Data.DriverId,
			},
		},
	)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to send image message", slog.String("serviceId", serviceId.String()), slog.String("chatId", payload.To), slog.String("url", payload.Url), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Now()

	return &adapter_types.SendMessageResponse{
		MessageId: response.Id,
		Timestamp: &timestamp,
		Ack:       "0",
	}, nil
}

func (a *WhatsappAdapter) SendVideo(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendVideoPayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in SendVideo", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	response, err := a.grpcWppClient.SendMessage(ctx,
		&pb_common.SendMessageRequest{
			Payload: &pb_common.SendMessagePayload{
				ChatId:          payload.To,
				Text:            payload.Caption,
				QuotedMessageId: payload.ReplyMessageId,
				File: &pb_common.File{
					Url:      payload.Url,
					Mimetype: payload.Mimetype,
					Name:     payload.Filename,
				},
			},
			Metadata: &pb_common.Metadata{
				ServiceId: service.Data.DriverId,
			},
		},
	)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to send video message", slog.String("serviceId", serviceId.String()), slog.String("chatId", payload.To), slog.String("url", payload.Url), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Now()

	return &adapter_types.SendMessageResponse{
		MessageId: response.Id,
		Timestamp: &timestamp,
		Ack:       "0",
	}, nil
}

func (a *WhatsappAdapter) SendDocument(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendDocumentPayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in SendDocument", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	response, err := a.grpcWppClient.SendMessage(ctx,
		&pb_common.SendMessageRequest{
			Payload: &pb_common.SendMessagePayload{
				ChatId:          payload.To,
				Text:            payload.Caption,
				QuotedMessageId: payload.ReplyMessageId,
				File: &pb_common.File{
					Url:        payload.Url,
					Mimetype:   payload.Mimetype,
					Name:       payload.Filename,
					AsDocument: true,
				},
			},
			Metadata: &pb_common.Metadata{
				ServiceId: service.Data.DriverId,
			},
		},
	)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to send document message", slog.String("serviceId", serviceId.String()), slog.String("chatId", payload.To), slog.String("url", payload.Url), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Now()

	return &adapter_types.SendMessageResponse{
		MessageId: response.Id,
		Timestamp: &timestamp,
		Ack:       "0",
	}, nil
}

func (a *WhatsappAdapter) BuildMessage(ctx context.Context, payload *WebhookMessagePayload) (*adapter_types.WebhookMessage, error) {
	msgType, err := getWebhookMessageType(ctx, payload)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get webhook message type", slog.String("payloadType", string(payload.Type)), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get webhook message type: %w", err)
	}

	var file *adapter_types.WebhookMessageFile

	if payload.File != nil {
		file = &adapter_types.WebhookMessageFile{
			Mimetype: payload.File.Mimetype,
			Url:      payload.File.Url,
		}
	}

	var location *adapter_types.WebhookMessageLocation

	if payload.Data.Location != nil && payload.Data.Location.Lat != 0 {
		location = &adapter_types.WebhookMessageLocation{
			Lat:        payload.Data.Location.Lat,
			Lng:        payload.Data.Location.Lng,
			PreviewUrl: payload.Data.Location.MapPreviewUrl,
		}
	}

	var ctwaContex *adapter_types.CtwaContext

	if payload.Data.CtwaContext != nil {
		ctwaContex = &adapter_types.CtwaContext{
			ConversionSource: payload.Data.CtwaContext.ConversionSource,
			Description:      payload.Data.CtwaContext.Description,
			IsSuspiciousLink: payload.Data.CtwaContext.IsSuspiciousLink,
			MediaUrl:         payload.Data.CtwaContext.MediaUrl,
			SourceUrl:        payload.Data.CtwaContext.SourceUrl,
			ThumbnailUrl:     payload.Data.CtwaContext.ThumbnailUrl,
			Title:            payload.Data.CtwaContext.Title,
			MediaType:        payload.Data.CtwaContext.MediaType,
		}
	}

	replyMessageId := ""
	var replyMessageWebhook *adapter_types.BuiltWebhook

	if payload.QuotedMessage != nil {
		replyMessageId = payload.QuotedMessage.Id

		builtReplyMessage, err := a.BuildMessage(ctx, payload.QuotedMessage)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to build quoted message", slog.String("messageId", payload.QuotedMessage.Id), slog.Any("error", err))
			return nil, fmt.Errorf("failed to build quoted message: %w", err)
		}

		var builtContact *adapter_types.WebhookContact

		if payload.QuotedMessage.IsStatus {
			builtContact, err = a.BuildContact(payload.Contact)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to build contact for quoted message (status)", slog.Any("contact", payload.Contact), slog.Any("error", err))
				return nil, fmt.Errorf("failed to build contact for quoted message: %w", err)
			}
		} else {
			builtContact, err = a.BuildContact(payload.QuotedMessage.Contact)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to build contact for quoted message", slog.Any("contact", payload.QuotedMessage.Contact), slog.Any("error", err))
				return nil, fmt.Errorf("failed to build contact for quoted message: %w", err)
			}
		}

		var builtFrom *adapter_types.WebhookContact

		if payload.From == nil {
			builtFrom = builtContact
		} else {
			builtFrom, err = a.BuildFrom(payload.QuotedMessage.From, payload.QuotedMessage.Contact)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to build from for quoted message", slog.Any("from", payload.QuotedMessage.From), slog.Any("error", err))
				return nil, fmt.Errorf("failed to build from for quoted message: %w", err)
			}
		}

		replyMessageWebhook = &adapter_types.BuiltWebhook{
			Message: builtReplyMessage,
			Contact: builtContact,
			From:    builtFrom,
		}
	}

	return &adapter_types.WebhookMessage{
		Id:                  payload.Id,
		Type:                msgType,
		Text:                payload.Text,
		Timestamp:           payload.Timestamp,
		ReplyMessageId:      replyMessageId,
		ReplyMessageWebhook: replyMessageWebhook,
		IsFromMe:            payload.IsFromMe,
		File:                file,
		Vcard:               payload.Data.Vcard,
		Location:            location,
		CtwaContext:         ctwaContex,
	}, nil
}

func (a *WhatsappAdapter) BuildContact(payload *Contact) (*adapter_types.WebhookContact, error) {
	var contact *adapter_types.WebhookContact

	if payload != nil {
		contact = &adapter_types.WebhookContact{
			Id:              payload.Id,
			Name:            payload.Name,
			AlternativeName: payload.ProfileName,
			IsGroup:         payload.IsGroup,
			IsMe:            payload.IsMe,
			Visible:         true,
			AvatarUrl:       payload.AvatarUrl,
		}
	}

	return contact, nil
}

func (a *WhatsappAdapter) BuildFrom(payloadFrom *Contact, payloadContact *Contact) (*adapter_types.WebhookContact, error) {
	from := &adapter_types.WebhookContact{
		Id:              payloadFrom.Id,
		Name:            payloadFrom.Name,
		AlternativeName: payloadFrom.ProfileName,
		IsGroup:         payloadFrom.IsGroup,
		IsMe:            payloadFrom.IsMe,
		Visible:         false,
		AvatarUrl:       payloadContact.AvatarUrl,
	}
	return from, nil
}

func (a *WhatsappAdapter) BuildMessageWebhook(ctx context.Context, payload *WebhookMessagePayload) (*adapter_types.BuiltWebhook, error) {
	ack, err := parseAckToString(ctx, payload.Ack)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to parse ack in BuildMessageWebhook", slog.Any("ack", payload.Ack), slog.Any("error", err))
		return nil, fmt.Errorf("failed to parse ack: %w", err)
	}

	if payload.Error != "" {
		if payload.Id != "" {
			return &adapter_types.BuiltWebhook{
				Statuses: &adapter_types.WebhookStatuses{
					Error: &models.MessageError{
						Error:         payload.Error,
						Code:          500,
						Message:       payload.Error,
						OriginalError: payload.Error,
					},
					Ack:       ack,
					Id:        payload.Id,
					Timestamp: payload.Timestamp,
				},
			}, nil
		}
	}

	var dataAck interface{}
	if payload.Data != nil {
		dataAck = payload.Data.Ack
	}

	newAck, err := parseAckToString(ctx, dataAck)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to parse data ack in BuildMessageWebhook", slog.Any("dataAck", dataAck), slog.Any("error", err))
		return nil, fmt.Errorf("failed to parse data ack: %w", err)
	}

	contact, err := a.BuildContact(payload.Contact)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to build contact in BuildMessageWebhook", slog.Any("contact", payload.Contact), slog.Any("error", err))
		return nil, fmt.Errorf("failed to build contact: %w", err)
	}

	var from *adapter_types.WebhookContact
	if payload.From == nil {
		from = contact
	} else {
		from, err = a.BuildFrom(payload.From, payload.Contact)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to build from in BuildMessageWebhook", slog.Any("from", payload.From), slog.Any("error", err))
			return nil, fmt.Errorf("failed to build from: %w", err)
		}
	}

	builtMessage, err := a.BuildMessage(ctx, payload)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to build message in BuildMessageWebhook", slog.String("messageId", payload.Id), slog.Any("error", err))
		return nil, fmt.Errorf("failed to build message: %w", err)
	}

	return &adapter_types.BuiltWebhook{
		Message:  builtMessage,
		Contact:  contact,
		From:     from,
		Statuses: &adapter_types.WebhookStatuses{Ack: newAck, Id: payload.Id, Timestamp: payload.Timestamp},
	}, nil
}

func (a *WhatsappAdapter) BuildMessageSingle(ctx context.Context, messageData *WebhookMessagePayload) ([]*adapter_types.BuiltWebhook, error) {
	// Transforma multi_vcard em vários vcard
	if messageData.Type == "multi_vcard" {
		var builtMessages []*adapter_types.BuiltWebhook

		for index, v := range messageData.Data.Vcards {
			newMsgData := *messageData // Cria um novo endereço na memória
			newMsgData.Id = fmt.Sprintf("%s%d", messageData.Id, index)
			newMsgData.Data.Vcard = v
			newMsgData.Type = "vcard"
			newTimestamp := newMsgData.Timestamp.Add(time.Duration(index) * time.Millisecond)
			newMsgData.Timestamp = &newTimestamp

			builtMessage, err := a.BuildMessageWebhook(ctx, &newMsgData)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to build message webhook for multi_vcard", slog.String("newMsgDataId", newMsgData.Id), slog.Any("error", err))
				return nil, fmt.Errorf("failed to build message webhook for multi_vcard: %w", err)
			}
			builtMessages = append(builtMessages, builtMessage)
		}

		return builtMessages, nil
	}

	builtMessage, err := a.BuildMessageWebhook(ctx, messageData)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to build message webhook", slog.String("messageId", messageData.Id), slog.Any("error", err))
		return nil, fmt.Errorf("failed to build message webhook: %w", err)
	}

	return []*adapter_types.BuiltWebhook{builtMessage}, nil
}

func (a *WhatsappAdapter) BuildWebhook(ctx context.Context, serviceId uuid.UUID, payload interface{}) ([]*adapter_types.BuiltWebhook, error) {
	var p *WhatsappWebhookPayload

	err := common.ToStruct(ctx, payload, &p)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to convert payload to WhatsappWebhookPayload", slog.Any("payload", payload), slog.Any("error", err))
		return nil, fmt.Errorf("failed to convert payload: %w", err)
	}

	webhookType, err := getWebhookType(ctx, p)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get webhook type", slog.Any("payload", p.Payload), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get webhook type: %w", err)
	}

	if webhookType == "service" {
		var serviceData *WebhookServicePayload

		err = common.ToStruct(ctx, p.Payload.Data, &serviceData)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to convert payload data to WebhookServicePayload", slog.Any("data", p.Payload.Data), slog.Any("error", err))
			return nil, fmt.Errorf("failed to convert payload data to WebhookServicePayload: %w", err)
		}

		status := serviceData.Data.Status

		return []*adapter_types.BuiltWebhook{{
			Service: &adapter_types.WebhookService{
				Data: &models.ServiceData{
					MyId: status.MyId,
					Status: &models.ServiceDataStatus{
						IsSyncing:           status.IsWebSyncing,
						IsConnected:         status.IsConnected,
						IsStarting:          false,
						IsStarted:           true,
						IsConflicted:        status.IsConflicted,
						IsLoading:           status.IsLoading,
						IsOnChatPage:        status.IsOnChatPage,
						IsOnQrPage:          status.IsOnQrPage,
						IsQrCodeExpired:     status.IsQrCodeExpired,
						IsWebConnected:      status.IsWebConnected,
						IsWebSyncing:        status.IsWebSyncing,
						Mode:                status.Mode,
						MyId:                status.MyId,
						MyName:              status.MyName,
						MyNumber:            status.MyNumber,
						QrCodeExpiresAt:     status.QrCodeExpiresAt,
						QrCodeUrl:           status.QrCodeUrl,
						State:               status.State,
						EnteredQrCodePageAt: status.EnteredQrCodePageAt,
						DisconnectedAt:      status.DisconnectedAt,
						WaVersion:           status.WaVersion,
					},
				},
			},
		}}, nil
	}

	if webhookType == "message" {
		var messageData *WebhookMessagePayload

		err = common.ToStruct(ctx, p.Payload.Data, &messageData)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to convert payload data to WebhookMessagePayload", slog.Any("data", p.Payload.Data), slog.Any("error", err))
			var messagesData []*WebhookMessagePayload
			var builtWebhooks []*adapter_types.BuiltWebhook

			err = common.ToStruct(ctx, p.Payload.Data, &messagesData)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to convert payload data to slice of WebhookMessagePayload", slog.Any("data", p.Payload.Data), slog.Any("error", err))
				return nil, fmt.Errorf("failed to convert payload data to WebhookMessagePayload: %w", err)
			}

			for _, msgData := range messagesData {
				btWebhooks, err := a.BuildMessageSingle(ctx, msgData)
				if err != nil {
					slog.ErrorContext(ctx, "Failed to build message single in BuildWebhook", slog.String("messageId", msgData.Id), slog.Any("error", err))
					return nil, fmt.Errorf("failed to build message single: %w", err)
				}

				builtWebhooks = append(builtWebhooks, btWebhooks...)
			}
			return builtWebhooks, nil
		}

		builtWebhook, err := a.BuildMessageSingle(ctx, messageData)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to build message single in BuildWebhook", slog.String("messageId", messageData.Id), slog.Any("error", err))
			return nil, fmt.Errorf("failed to build message single: %w", err)
		}

		return builtWebhook, nil
	}

	if webhookType == "statuses" {
		var messageData *WebhookMessagePayload

		err = common.ToStruct(ctx, p.Payload.Data, &messageData)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to convert payload data to WebhookMessagePayload for statuses", slog.Any("data", p.Payload.Data), slog.Any("error", err))
			return nil, fmt.Errorf("failed to convert payload data for statuses: %w", err)
		}

		ack, err := parseAckToString(ctx, messageData.Data.Ack)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to parse ack for statuses", slog.Any("ack", messageData.Data.Ack), slog.Any("error", err))
			return nil, fmt.Errorf("failed to parse ack for statuses: %w", err)
		}

		if messageData.Id != "" {
			return []*adapter_types.BuiltWebhook{{
				Statuses: &adapter_types.WebhookStatuses{
					Ack:       ack,
					Id:        messageData.Id,
					Timestamp: messageData.Timestamp,
				},
			}}, nil
		}
	}

	if webhookType == "reaction" {
		var reactionData *WebhookReactionPayload

		err = common.ToStruct(ctx, p.Payload.Data, &reactionData)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to convert payload data to WebhookReactionPayload", slog.Any("data", p.Payload.Data), slog.Any("error", err))
			return nil, fmt.Errorf("failed to convert payload data to WebhookReactionPayload: %w", err)
		}

		contact, err := a.BuildContact(reactionData.Contact)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to build contact for reaction", slog.Any("contact", reactionData.Contact), slog.Any("error", err))
			return nil, fmt.Errorf("failed to build contact for reaction: %w", err)
		}

		var from *adapter_types.WebhookContact

		if reactionData.From == nil {
			from = contact
		} else {
			from, err = a.BuildFrom(reactionData.From, reactionData.From)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to build from for reaction", slog.Any("from", reactionData.From), slog.Any("error", err))
				return nil, fmt.Errorf("failed to build from for reaction: %w", err)
			}
		}
		reactionIsFromMe := strings.HasPrefix(reactionData.MessageId, "true")

		return []*adapter_types.BuiltWebhook{{
			Reaction: &adapter_types.WebhookReaction{
				NewReactions: []*adapter_types.Reaction{{Emoji: reactionData.Reaction}},
				Timestamp:    reactionData.Timestamp,
				IsFromMe:     reactionIsFromMe,
			},
			Message: &adapter_types.WebhookMessage{
				Id:       reactionData.MessageId,
				Type:     "reaction",
				IsFromMe: reactionData.IsFromMe,
			},
			Contact: contact,
			From:    from,
		}}, nil
	}

	return nil, fmt.Errorf("unhandled webhook type: %s", webhookType)
}

func (a *WhatsappAdapter) DownloadMedia(ctx context.Context, serviceId uuid.UUID, mediaId string, mediaUrl string) (*adapter_types.DownloadMediaResponse, error) {
	resp, err := http.Get(mediaUrl)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to download media", slog.String("mediaUrl", mediaUrl), slog.Any("error", err))
		return nil, fmt.Errorf("failed to download media from url %s: %w", mediaUrl, err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to read media response body", slog.String("mediaUrl", mediaUrl), slog.Any("error", err))
		return nil, fmt.Errorf("failed to read media data from url %s: %w", mediaUrl, err)
	}

	response := &adapter_types.DownloadMediaResponse{
		Type:   "stream",
		Stream: bytes.NewReader(data),
	}

	return response, nil
}

func (a *WhatsappAdapter) LoadEarlierMessages(ctx context.Context, serviceId uuid.UUID, loadPayload *adapter_types.LoadEarlierMessagesPayload) ([]*adapter_types.BuiltWebhook, error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in LoadEarlierMessages", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	payload := &pb_common.LoadEarlierMessagesTillDateRequest{
		Payload: &pb_common.LoadEarlierMessagesTillDatePayload{
			ContactId: loadPayload.IdFromService,
			Timestamp: loadPayload.Timestamp.Unix(),
		},
		Metadata: &pb_common.Metadata{
			ServiceId: service.Data.DriverId,
		},
	}

	response, err := a.grpcWppClient.LoadEarlierMessagesTillDate(ctx, payload)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to load earlier messages via gRPC", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to load earlier messages: %w", err)
	}

	var buitWebhooks []*adapter_types.BuiltWebhook

	for _, msg := range response.Messages {
		timestamp, err := time.Parse(time.RFC3339, msg.Timestamp)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to parse timestamp in LoadEarlierMessages", slog.String("timestamp", msg.Timestamp), slog.Any("error", err))
			return nil, fmt.Errorf("failed to parse timestamp: %w", err)
		}

		var from *Contact
		if msg.From != nil {
			from = &Contact{
				Id:          msg.From.Id,
				Name:        msg.From.Name,
				IsGroup:     msg.From.IsGroup,
				ProfileName: msg.From.ProfileName,
				IsMe:        msg.From.IsMe,
			}
		}

		var quotedMessage *WebhookMessagePayload
		if msg.QuotedMessage != nil {
			quotedMessage = &WebhookMessagePayload{
				Id: msg.QuotedMessage.Id,
			}
		}

		var file *File
		if msg.File != nil {
			file = &File{
				Mimetype: msg.File.MimeType,
				Url:      msg.File.Url,
			}
		}

		builtMessage, err := a.BuildMessageWebhook(
			ctx,
			&WebhookMessagePayload{
				AccountId: service.AccountId,
				Type:      WebhookMessageTypeEnum(msg.Type),
				Ack:       msg.Ack,
				Data: &MessageData{
					Ack: msg.Data.Ack,
				},
				Id:            msg.Id,
				Text:          msg.Text,
				Timestamp:     &timestamp,
				Error:         "",
				IsFromMe:      msg.IsFromMe,
				QuotedMessage: quotedMessage,
				Contact: &Contact{
					Id:          msg.Contact.Id,
					Name:        msg.Contact.Name,
					Number:      msg.Contact.Number,
					ProfileName: msg.Contact.ProfileName,
					IsGroup:     msg.Contact.IsGroup,
					IsMe:        msg.Contact.IsMe,
				},
				From: from,
				File: file,
			},
		)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to build message webhook in LoadEarlierMessages", slog.String("messageId", msg.Id), slog.Any("error", err))
			return nil, fmt.Errorf("failed to build message webhook: %w", err)
		}

		buitWebhooks = append(buitWebhooks, builtMessage)
	}

	return buitWebhooks, nil
}

// Era usado apenas na Gestta
func (a *WhatsappAdapter) ForwardMessage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.ForwardMessagePayload) (*adapter_types.ForwardMessageResponse, error) {
	service, err := a.serviceRepository.FindById(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service in ForwardMessage", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	forwardPayload := &pb_common.ForwardMessageRequest{
		Payload: &pb_common.ForwardMessagePayload{
			ChatId:           payload.ToId,
			ForwardMessageId: payload.MessageId,
		},
		Metadata: &pb_common.Metadata{
			ServiceId: service.Data.DriverId,
		},
	}

	response, err := a.grpcWppClient.ForwardMessage(ctx, forwardPayload)
	slog.InfoContext(ctx, "ForwardMessage response", slog.Any("response", common.PrettyPrint(response)))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to forward message", slog.String("serviceId", serviceId.String()), slog.String("toId", payload.ToId), slog.String("messageId", payload.MessageId), slog.Any("error", err))
		return nil, fmt.Errorf("failed to forward message: %w", err)
	}

	return &adapter_types.ForwardMessageResponse{}, nil
}

func (a *WhatsappAdapter) SendTemplate(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendTemplatePayload) (*adapter_types.SendMessageResponse, error) {
	return nil, nil
}

func (a *WhatsappAdapter) SendInteractiveMessage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendInteractiveMessagePayload) (*adapter_types.SendMessageResponse, error) {
	return nil, nil
}

func (a *WhatsappAdapter) SendSticker(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendStickerPayload) (*adapter_types.SendMessageResponse, error) {
	return nil, nil
}

func (a *WhatsappAdapter) GetTemplates(ctx context.Context, serviceId uuid.UUID) ([]*models.WhatsappBusinessTemplate, error) {
	return nil, nil
}

func (a *WhatsappAdapter) CreateTemplate(ctx context.Context, serviceId uuid.UUID, templateId uuid.UUID) (*models.WhatsappBusinessTemplate, error) {
	return nil, nil
}

func (a *WhatsappAdapter) DeleteTemplate(ctx context.Context, serviceId uuid.UUID, templateId string) error {
	return nil
}
