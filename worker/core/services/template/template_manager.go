package template

import (
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/managers/service_manager"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/campaign"
	"fmt"
	"log/slog"
	"slices"
	"time"

	"gorm.io/gorm"
)

type TemplateManagerService struct {
	adapterManager            *service_manager.ServiceManager
	serviceRepository         repositories.ServiceRepository
	templateRepository        repositories.WhatsappBusinessTemplateRepository
	templateHistoryRepository repositories.WhatsappBusinessTemplateHistoryRepository
	campaignRepository        repositories.CampaignRepository
	accountRepository         repositories.AccountRepository
	config                    *config.Config
	campaignService           *campaign.CampaignService
}

func NewTemplateManagerService(
	adapterManager *service_manager.ServiceManager,
	serviceRepository repositories.ServiceRepository,
	templateRepository repositories.WhatsappBusinessTemplateRepository,
	templateHistoryRepository repositories.WhatsappBusinessTemplateHistoryRepository,
	campaignRepository repositories.CampaignRepository,
	accountRepository repositories.AccountRepository,
	config *config.Config,
	campaignService *campaign.CampaignService,
) *TemplateManagerService {
	return &TemplateManagerService{
		adapterManager:            adapterManager,
		serviceRepository:         serviceRepository,
		templateRepository:        templateRepository,
		templateHistoryRepository: templateHistoryRepository,
		campaignRepository:        campaignRepository,
		accountRepository:         accountRepository,
		config:                    config,
		campaignService:           campaignService,
	}
}

// areComponentsEqual verifica se dois conjuntos de componentes são iguais
func (m *TemplateManagerService) areComponentsEqual(components1, components2 []*models.WhatsappBusinessComponent) bool {
	if len(components1) != len(components2) {
		return false
	}

	// Mapear componentes por tipo para facilitar a comparação
	componentMap1 := make(map[string]*models.WhatsappBusinessComponent)
	componentMap2 := make(map[string]*models.WhatsappBusinessComponent)

	for _, comp := range components1 {
		componentMap1[comp.Type] = comp
	}

	for _, comp := range components2 {
		componentMap2[comp.Type] = comp
	}

	// Verificar se todos os tipos de componentes são iguais
	for typ, comp1 := range componentMap1 {
		comp2, exists := componentMap2[typ]
		if !exists {
			return false
		}

		// Verificar se os textos são iguais
		if comp1.Text != comp2.Text {
			return false
		}

		// Verificar se os formatos são iguais
		if comp1.Format != comp2.Format {
			return false
		}

		// Verificar se os botões são iguais
		if !m.areButtonsEqual(comp1.Buttons, comp2.Buttons) {
			return false
		}
	}

	return true
}

// areButtonsEqual verifica se dois conjuntos de botões são iguais
func (m *TemplateManagerService) areButtonsEqual(buttons1, buttons2 []*models.WhatsappBusinessComponentParameterButton) bool {
	if len(buttons1) != len(buttons2) {
		return false
	}

	// Mapear botões por tipo para facilitar a comparação
	buttonMap1 := make(map[string]*models.WhatsappBusinessComponentParameterButton)
	buttonMap2 := make(map[string]*models.WhatsappBusinessComponentParameterButton)

	for i, btn := range buttons1 {
		buttonMap1[fmt.Sprintf("%s_%d", btn.Type, i)] = btn
	}

	for i, btn := range buttons2 {
		buttonMap2[fmt.Sprintf("%s_%d", btn.Type, i)] = btn
	}

	// Verificar se todos os botões são iguais
	for key, btn1 := range buttonMap1 {
		btn2, exists := buttonMap2[key]
		if !exists {
			return false
		}

		// Verificar se os tipos são iguais
		if btn1.Type != btn2.Type {
			return false
		}

		// Verificar se os textos são iguais
		if btn1.Text != btn2.Text {
			return false
		}

		// Verificar se os números de telefone são iguais
		if btn1.PhoneNumber != btn2.PhoneNumber {
			return false
		}

		// Verificar se as URLs são iguais
		if btn1.URL != btn2.URL {
			return false
		}
	}

	return true
}

// contains verifica se uma string está contida em uma lista de strings
func (m *TemplateManagerService) contains(list []string, item string) bool {
	return slices.Contains(list, item)
}

// preserveComponentParams preserva os parâmetros internos dos componentes
func (m *TemplateManagerService) preserveComponentParams(newComponents, existingComponents []*models.WhatsappBusinessComponent) {
	// Mapear componentes existentes por tipo para facilitar a busca
	existingComponentMap := make(map[string]*models.WhatsappBusinessComponent)
	for _, comp := range existingComponents {
		existingComponentMap[comp.Type] = comp
	}

	// Para cada componente novo, verificar se existe um componente correspondente
	// e preservar os parâmetros internos
	for _, newComp := range newComponents {
		existingComp, exists := existingComponentMap[newComp.Type]
		if exists && len(existingComp.Params) > 0 {
			// Preservar os parâmetros internos
			newComp.Params = existingComp.Params
		}
	}
}

func (m *TemplateManagerService) SyncTemplates(ctx context.Context, syncData *payloads.SyncTemplatesPayload) (response any, err error) {
	serviceAdapter, err := m.adapterManager.GetAdapter(ctx, syncData.ServiceId)
	if err != nil {
		slog.ErrorContext(ctx, "SyncTemplates get adapter failed", slog.String("error", err.Error()), slog.Any("syncData", syncData))
		return false, fmt.Errorf("SyncTemplates get adapter failed: %w", err)
	}

	// Obter o serviço para verificar o tipo
	service, err := m.serviceRepository.FindById(ctx, syncData.ServiceId)
	if err != nil {
		slog.ErrorContext(ctx, "SyncTemplates get service failed", slog.String("error", err.Error()), slog.Any("syncData", syncData))
		return false, fmt.Errorf("SyncTemplates get service failed: %w", err)
	}

	// Verificar se o serviço suporta templates
	if service.Type != "whatsapp-business" {
		slog.InfoContext(ctx, "Service type does not support templates", slog.String("serviceType", service.Type), slog.Any("syncData", syncData))
		return []*models.WhatsappBusinessTemplate{}, nil
	}

	// Obter templates do provedor
	templates, err := serviceAdapter.GetTemplates(ctx, syncData.ServiceId)
	if err != nil {
		slog.ErrorContext(ctx, "SyncTemplates get templates failed", slog.String("error", err.Error()), slog.Any("syncData", syncData))
		return false, fmt.Errorf("SyncTemplates get templates failed: %w", err)
	}

	slog.InfoContext(ctx, "Templates retrieved from provider", slog.Int("count", len(templates)), slog.Any("syncData", syncData))

	// 1. Buscar templates existentes no banco de dados
	existingTemplates, err := m.templateRepository.FindMany(ctx, repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Where(`"serviceId" = ?`, syncData.ServiceId)
	}))
	if err != nil {
		slog.ErrorContext(ctx, "SyncTemplates find existing templates failed", slog.String("error", err.Error()), slog.Any("syncData", syncData))
		return false, fmt.Errorf("SyncTemplates find existing templates failed: %w", err)
	}

	// Mapear templates existentes por nome e idioma para facilitar a comparação
	existingTemplateMap := make(map[string]*models.WhatsappBusinessTemplate)

	for i := range existingTemplates {
		// Chave composta de nome e idioma para identificar unicamente o template
		key := existingTemplates[i].Name + "_" + existingTemplates[i].Language
		existingTemplateMap[key] = existingTemplates[i]
	}

	// 2. Comparar templates do provedor com os do banco
	var templatesToUpdate []*models.WhatsappBusinessTemplate
	var templateHistoryToCreate []*models.WhatsappBusinessTemplateHistory
	var templatesToCreate []*models.WhatsappBusinessTemplate
	var templatesToPauseCampaign []*models.WhatsappBusinessTemplate
	var processedTemplateKeys []string

	for _, template := range templates {
		key := template.Name + "_" + template.Language
		processedTemplateKeys = append(processedTemplateKeys, key)

		existingTemplate, exists := existingTemplateMap[key]
		if exists {
			// 3. Atualizar templates existentes
			// Preservar o Id do template existente
			template.Id = existingTemplate.Id

			// Verificar se o template foi modificado
			if template.Status != existingTemplate.Status ||
				template.Category != existingTemplate.Category ||
				template.Quality != existingTemplate.Quality ||
				!m.areComponentsEqual(template.Components, existingTemplate.Components) {

				// Preservar campos que não devem ser atualizados
				template.CreatedAt = existingTemplate.CreatedAt
				template.ArchivedAt = existingTemplate.ArchivedAt

				// Preservar os parâmetros internos dos componentes
				m.preserveComponentParams(template.Components, existingTemplate.Components)

				// Criar histórico se status ou quality mudaram
				if template.Status != existingTemplate.Status || template.Quality != existingTemplate.Quality {
					history := &models.WhatsappBusinessTemplateHistory{
						AccountId:      existingTemplate.AccountId,
						WabaTemplateId: existingTemplate.Id,
					}

					// Adicionar campos de status se houve mudança
					if template.Status != existingTemplate.Status {
						history.StatusFrom = existingTemplate.Status
						history.StatusTo = template.Status
					}

					if template.Quality != existingTemplate.Quality {
						// Adicionar campos de quality se houve mudança
						history.QualityFrom = string(existingTemplate.Quality)
						history.QualityTo = string(template.Quality)

						// Se o template foi aprovado e tem qualidade baixa ou média, adicionar à lista para verificar se deve pausar campanhas
						if template.Status == models.WhatsappBusinessTemplateStatusApproved && (template.Quality == models.WhatsappBusinessTemplateQualityLow || template.Quality == models.WhatsappBusinessTemplateQualityMedium) {
							// Adicionar template à lista de templates para pausar campanhas
							templatesToPauseCampaign = append(templatesToPauseCampaign, template)
						}
					}

					templateHistoryToCreate = append(templateHistoryToCreate, history)
				}

				templatesToUpdate = append(templatesToUpdate, template)
			}
		} else {
			// 4. Criar novos templates
			templatesToCreate = append(templatesToCreate, template)
		}
	}

	// 5. Arquivar templates que não existem mais no provedor
	var templatesToArchive []*models.WhatsappBusinessTemplate
	// 6. Desarquivar templates que existem no provedor mas estavam arquivados
	var templatesToUnarchived []*models.WhatsappBusinessTemplate
	now := time.Now()

	for key, template := range existingTemplateMap {
		// Verificar se o template não está na lista de templates processados
		if !m.contains(processedTemplateKeys, key) && template.ArchivedAt == nil {
			// Marcar como arquivado
			template.ArchivedAt = &now
			templatesToArchive = append(templatesToArchive, template)
		} else if m.contains(processedTemplateKeys, key) && template.ArchivedAt != nil {
			// Desarquivar template que existe no provedor mas estava arquivado
			var zeroTime *time.Time
			template.ArchivedAt = zeroTime // Só isso não resolve, o update precisa passar nil explicitamente
			templatesToUnarchived = append(templatesToUnarchived, template)
		}
	}

	// Criar novos templates
	createdCount := 0
	if len(templatesToCreate) > 0 {
		for _, template := range templatesToCreate {
			err = m.templateRepository.Create(ctx, template)
			if err != nil {
				slog.ErrorContext(ctx, "SyncTemplates create template failed",
					slog.String("error", err.Error()),
					slog.String("templateName", template.Name),
					slog.Any("syncData", syncData))
				// Continuar mesmo com erro para criar o máximo possível
			} else {
				createdCount++
			}
		}
		slog.InfoContext(ctx, "Templates created", slog.Int("count", createdCount), slog.Any("syncData", syncData))
	}

	// Atualizar templates existentes
	if len(templatesToUpdate) > 0 {
		for _, template := range templatesToUpdate {
			err = m.templateRepository.UpdateById(ctx, template.Id, template)
			if err != nil {
				slog.ErrorContext(ctx, "SyncTemplates update template failed",
					slog.String("error", err.Error()),
					slog.String("templateId", template.Id.String()),
					slog.Any("syncData", syncData))
				// Continuar mesmo com erro para atualizar o máximo possível
			}
		}
		slog.InfoContext(ctx, "Templates updated", slog.Int("count", len(templatesToUpdate)), slog.Any("syncData", syncData))
	}

	// Criar registros de histórico
	if len(templateHistoryToCreate) > 0 {
		for _, history := range templateHistoryToCreate {
			err = m.templateHistoryRepository.Create(ctx, history)
			if err != nil {
				slog.ErrorContext(ctx, "SyncTemplates create template history failed",
					slog.String("error", err.Error()),
					slog.String("wabaTemplateId", history.WabaTemplateId.String()),
					slog.Any("syncData", syncData))
				// Continuar mesmo com erro para criar o máximo possível de históricos
			}
		}
		slog.InfoContext(ctx, "Template history records created", slog.Int("count", len(templateHistoryToCreate)), slog.Any("syncData", syncData))
	}

	// Arquivar templates que não existem mais
	if len(templatesToArchive) > 0 {
		for _, template := range templatesToArchive {
			err = m.templateRepository.UpdateById(ctx, template.Id, template)
			if err != nil {
				slog.ErrorContext(ctx, "SyncTemplates archive template failed",
					slog.String("error", err.Error()),
					slog.String("templateId", template.Id.String()),
					slog.Any("syncData", syncData))
				// Continuar mesmo com erro para arquivar o máximo possível
			}
		}
		slog.InfoContext(ctx, "Templates archived", slog.Int("count", len(templatesToArchive)), slog.Any("syncData", syncData))
	}

	// Desarquivar templates que existem novamente
	if len(templatesToUnarchived) > 0 {
		for _, template := range templatesToUnarchived {
			// Usar WithQuery para forçar a atualização do campo ArchivedAt para NULL
			err = m.templateRepository.UpdateById(ctx, template.Id, template)
			// 	, repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
			// 	return db.Model(template).Updates(map[string]any{
			// 		"archivedAt": nil, // Força a atualização para NULL no banco
			// 	})
			// }))
			if err != nil {
				slog.ErrorContext(ctx, "SyncTemplates unarchive template failed",
					slog.String("error", err.Error()),
					slog.String("templateId", template.Id.String()),
					slog.Any("syncData", syncData))
				// Continuar mesmo com erro para desarquivar o máximo possível
			}
		}
		slog.InfoContext(ctx, "Templates unarchived", slog.Int("count", len(templatesToUnarchived)), slog.Any("syncData", syncData))
	}

	// Pausar campanhas para templates com qualidade baixa ou média
	pausedCampaignsCount := 0
	if len(templatesToPauseCampaign) > 0 {
		for _, template := range templatesToPauseCampaign {
			// Usar o serviço de campanha para pausar campanhas
			count, err := m.campaignService.PauseCampaignsFromTemplateId(
				ctx,
				template.Id,
				template.AccountId,
				template.ServiceId,
				template.Quality,
			)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to pause campaigns for template",
					slog.String("error", err.Error()),
					slog.String("templateId", template.Id.String()),
					slog.Any("syncData", syncData))
				continue // Continuar mesmo com erro para processar outros templates
			}
			pausedCampaignsCount += count
		}
		slog.InfoContext(ctx, "Campaigns paused due to low or medium quality templates",
			slog.Int("count", pausedCampaignsCount),
			slog.Any("syncData", syncData))
	}

	result := map[string]any{
		"created":         createdCount,
		"updated":         len(templatesToUpdate),
		"archived":        len(templatesToArchive),
		"unarchived":      len(templatesToUnarchived),
		"pausedCampaigns": pausedCampaignsCount,
		"total":           len(templates),
	}

	return result, nil
}

func (m *TemplateManagerService) CreateTemplate(ctx context.Context, createData *payloads.CreateTemplatePayload) (response bool, err error) {
	serviceAdapter, err := m.adapterManager.GetAdapter(ctx, createData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "CreateTemplate get adapter failed", slog.String("error", err.Error()), slog.Any("createData", createData))
		return false, fmt.Errorf("CreateTemplate get adapter failed: %w", err)
	}

	err = m.templateRepository.UpdateById(ctx, createData.TemplateId, &models.WhatsappBusinessTemplate{
		Status: models.WhatsappBusinessTemplateStatusSending,
	}, nil, repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Where("status = ?", models.WhatsappBusinessTemplateStatusEmpty)
	}))

	if err != nil {
		slog.ErrorContext(ctx, "CreateTemplate update template failed", slog.String("error", err.Error()), slog.Any("createData", createData))
		return false, fmt.Errorf("CreateTemplate update template failed: %w", err)
	}

	template, err := serviceAdapter.CreateTemplate(ctx, createData.ServiceId, createData.TemplateId)

	if err != nil {
		err2 := m.templateRepository.UpdateById(ctx, createData.TemplateId, &models.WhatsappBusinessTemplate{
			Status:         models.WhatsappBusinessTemplateStatusError,
			RejectedReason: err.Error()[:255], // Limitar o tamanho do RejectedReason para 255 caracteres (limite do banco)
		}, nil, repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
			return db.Where("status = ?", models.WhatsappBusinessTemplateStatusSending)
		}))

		if err2 != nil {
			slog.ErrorContext(ctx, "CreateTemplate revert template status failed", slog.String("error", err2.Error()), slog.Any("createData", createData))
		}

		slog.ErrorContext(ctx, "CreateTemplate create template failed", slog.String("error", err.Error()), slog.Any("createData", createData))
		return false, fmt.Errorf("CreateTemplate create template failed: %w", err)
	}

	// Verificar se o template foi rejeitado (status code 400 case)
	if template.Status == models.WhatsappBusinessTemplateStatusRejected {
		// Limitar o tamanho do RejectedReason para 255 caracteres (limite do banco)
		if len(template.RejectedReason) > 255 {
			template.RejectedReason = template.RejectedReason[:255]
		}

		// Atualizar o template no banco de dados com as informações atualizadas
		updateErr := m.templateRepository.UpdateById(ctx, template.Id, template)
		if updateErr != nil {
			slog.ErrorContext(ctx, "Failed to update template after rejection",
				slog.String("templateId", template.Id.String()),
				slog.String("error", updateErr.Error()),
				slog.Any("createData", createData))
			// Continuar mesmo com erro, pois o template já foi atualizado com o rejetedReason e status REJECTED
		}

		slog.InfoContext(ctx, "Template was rejected by the provider",
			slog.String("templateId", template.Id.String()),
			slog.String("rejectedReason", template.RejectedReason),
			slog.Any("createData", createData))
	}

	return true, nil
}

func (m *TemplateManagerService) DeleteTemplate(ctx context.Context, deleteData *payloads.DeleteTemplatePayload) (response bool, err error) {
	serviceAdapter, err := m.adapterManager.GetAdapter(ctx, deleteData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "DeleteTemplate get adapter failed", slog.String("error", err.Error()), slog.Any("deleteData", deleteData))
		return false, fmt.Errorf("DeleteTemplate get adapter failed: %w", err)
	}

	template, err := m.templateRepository.FindById(ctx, deleteData.TemplateId)

	if err != nil {
		slog.ErrorContext(ctx, "DeleteTemplate find template failed", slog.String("error", err.Error()), slog.Any("deleteData", deleteData))
		return false, fmt.Errorf("DeleteTemplate find template failed: %w", err)
	}

	if template.Status == models.WhatsappBusinessTemplateStatusEmpty {
		err = m.templateRepository.DeleteById(ctx, deleteData.TemplateId, nil)

		if err != nil {
			slog.ErrorContext(ctx, "DeleteTemplate delete template failed", slog.String("error", err.Error()), slog.Any("deleteData", deleteData))
			return false, fmt.Errorf("DeleteTemplate delete template failed: %w", err)
		}

		return true, nil
	}

	err = serviceAdapter.DeleteTemplate(ctx, deleteData.ServiceId, template.Name)

	if err != nil {
		slog.ErrorContext(ctx, "DeleteTemplate delete template failed", slog.String("error", err.Error()), slog.Any("deleteData", deleteData))
		return false, fmt.Errorf("DeleteTemplate delete template failed: %w", err)
	}

	return true, nil
}
