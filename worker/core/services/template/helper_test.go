//go:build unit
// +build unit

package template

import (
	"testing"

	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"

	"github.com/stretchr/testify/assert"
)

func TestContains(t *testing.T) {
	service := &TemplateManagerService{}

	tests := []struct {
		name     string
		list     []string
		item     string
		expected bool
	}{
		{
			name:     "should find item in list",
			list:     []string{"a", "b", "c"},
			item:     "b",
			expected: true,
		},
		{
			name:     "should not find item in list",
			list:     []string{"a", "b", "c"},
			item:     "d",
			expected: false,
		},
		{
			name:     "should handle empty list",
			list:     []string{},
			item:     "a",
			expected: false,
		},
		{
			name:     "should handle empty item",
			list:     []string{"a", "b", "c"},
			item:     "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.contains(tt.list, tt.item)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAreButtonsEqual(t *testing.T) {
	service := &TemplateManagerService{}

	tests := []struct {
		name     string
		buttons1 []*models.WhatsappBusinessComponentParameterButton
		buttons2 []*models.WhatsappBusinessComponentParameterButton
		expected bool
	}{
		{
			name: "identical buttons",
			buttons1: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type: "URL",
					Text: "Visit us",
					URL:  "https://example.com",
				},
			},
			buttons2: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type: "URL",
					Text: "Visit us",
					URL:  "https://example.com",
				},
			},
			expected: true,
		},
		{
			name: "different button types",
			buttons1: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type: "URL",
					Text: "Visit us",
				},
			},
			buttons2: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type: "PHONE_NUMBER",
					Text: "Visit us",
				},
			},
			expected: false,
		},
		{
			name: "different lengths",
			buttons1: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type: "URL",
					Text: "Button 1",
				},
			},
			buttons2: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type: "URL",
					Text: "Button 1",
				},
				{
					Type: "URL",
					Text: "Button 2",
				},
			},
			expected: false,
		},
		{
			name:     "both empty",
			buttons1: []*models.WhatsappBusinessComponentParameterButton{},
			buttons2: []*models.WhatsappBusinessComponentParameterButton{},
			expected: true,
		},
		{
			name: "different phone numbers",
			buttons1: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type:        "PHONE_NUMBER",
					Text:        "Call us",
					PhoneNumber: "+5511999999999",
				},
			},
			buttons2: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type:        "PHONE_NUMBER",
					Text:        "Call us",
					PhoneNumber: "+5511888888888",
				},
			},
			expected: false,
		},
		{
			name:     "nil vs empty slice",
			buttons1: nil,
			buttons2: []*models.WhatsappBusinessComponentParameterButton{},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.areButtonsEqual(tt.buttons1, tt.buttons2)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAreComponentsEqual(t *testing.T) {
	service := &TemplateManagerService{}

	tests := []struct {
		name        string
		components1 []*models.WhatsappBusinessComponent
		components2 []*models.WhatsappBusinessComponent
		expected    bool
	}{
		{
			name: "identical components",
			components1: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeHeader,
					Text: "Welcome",
				},
			},
			components2: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeHeader,
					Text: "Welcome",
				},
			},
			expected: true,
		},
		{
			name: "different texts",
			components1: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeHeader,
					Text: "Welcome",
				},
			},
			components2: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeHeader,
					Text: "Welcome!",
				},
			},
			expected: false,
		},
		{
			name: "different formats",
			components1: []*models.WhatsappBusinessComponent{
				{
					Type:   adapter_types.ComponentTypeHeader,
					Format: "TEXT",
				},
			},
			components2: []*models.WhatsappBusinessComponent{
				{
					Type:   adapter_types.ComponentTypeHeader,
					Format: "IMAGE",
				},
			},
			expected: false,
		},
		{
			name:        "both empty",
			components1: []*models.WhatsappBusinessComponent{},
			components2: []*models.WhatsappBusinessComponent{},
			expected:    true,
		},
		{
			name: "different button counts",
			components1: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeButtons,
					Buttons: []*models.WhatsappBusinessComponentParameterButton{
						{Type: "URL", Text: "Button 1"},
					},
				},
			},
			components2: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeButtons,
					Buttons: []*models.WhatsappBusinessComponentParameterButton{
						{Type: "URL", Text: "Button 1"},
						{Type: "URL", Text: "Button 2"},
					},
				},
			},
			expected: false,
		},
		{
			name: "components with different types",
			components1: []*models.WhatsappBusinessComponent{
				{Type: adapter_types.ComponentTypeHeader},
				{Type: adapter_types.ComponentTypeBody},
			},
			components2: []*models.WhatsappBusinessComponent{
				{Type: adapter_types.ComponentTypeBody},
				{Type: adapter_types.ComponentTypeFooter},
			},
			expected: false,
		},
		{
			name:        "nil vs empty slice",
			components1: nil,
			components2: []*models.WhatsappBusinessComponent{},
			expected:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.areComponentsEqual(tt.components1, tt.components2)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestPreserveComponentParams(t *testing.T) {
	service := &TemplateManagerService{}

	tests := []struct {
		name               string
		newComponents      []*models.WhatsappBusinessComponent
		existingComponents []*models.WhatsappBusinessComponent
		expectedParams     []string
	}{
		{
			name: "preserve existing params",
			newComponents: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeBody,
					Text: "Hello {{1}}",
				},
			},
			existingComponents: []*models.WhatsappBusinessComponent{
				{
					Type:   adapter_types.ComponentTypeBody,
					Text:   "Hello {{1}}",
					Params: []string{"name"},
				},
			},
			expectedParams: []string{"name"},
		},
		{
			name: "no params to preserve",
			newComponents: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeBody,
					Text: "Hello",
				},
			},
			existingComponents: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeBody,
					Text: "Hello",
				},
			},
			expectedParams: nil,
		},
		{
			name: "different component types",
			newComponents: []*models.WhatsappBusinessComponent{
				{
					Type: adapter_types.ComponentTypeHeader,
					Text: "Title",
				},
			},
			existingComponents: []*models.WhatsappBusinessComponent{
				{
					Type:   adapter_types.ComponentTypeBody,
					Text:   "Content",
					Params: []string{"param1"},
				},
			},
			expectedParams: nil,
		},
		{
			name: "multiple components with params",
			newComponents: []*models.WhatsappBusinessComponent{
				{Type: adapter_types.ComponentTypeHeader},
				{Type: adapter_types.ComponentTypeBody},
			},
			existingComponents: []*models.WhatsappBusinessComponent{
				{
					Type:   adapter_types.ComponentTypeHeader,
					Params: []string{"header_param"},
				},
				{
					Type:   adapter_types.ComponentTypeBody,
					Params: []string{"body_param"},
				},
			},
			expectedParams: []string{"header_param"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service.preserveComponentParams(tt.newComponents, tt.existingComponents)
			assert.Equal(t, tt.expectedParams, tt.newComponents[0].Params)
		})
	}
}
