package message_forwarder

import (
	"context"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/managers/service_manager"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/crypto"
	"digisac-go/worker/core/services/message/message_sender"
	"digisac-go/worker/core/services/storage"
	"digisac-go/worker/core/utils/common"
	utilsconfig "digisac-go/worker/core/utils/config"
	streamutils "digisac-go/worker/core/utils/stream_utils"
	"fmt"
	"log/slog"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ForwardMessagesResponse struct {
	Id          uuid.UUID   `json:"id"`
	MessagesIds []uuid.UUID `json:"messagesIds"`
}

type ForwardService struct {
	adapterManager        *service_manager.ServiceManager
	contactRepository     repositories.ContactRepository
	messageRepository     repositories.MessageRepository
	accountRepository     repositories.AccountRepository
	fileRepository        repositories.FileRepository
	messageSendService    *message_sender.SendService
	accountCryptorService *crypto.AccountCryptor
	storageService        *storage.StorageService
}

func NewForwardService(
	adapterManager *service_manager.ServiceManager,
	contactRepository repositories.ContactRepository,
	messageRepository repositories.MessageRepository,
	accountRepository repositories.AccountRepository,
	fileRepository repositories.FileRepository,
	messageSendService *message_sender.SendService,
	accountCryptorService *crypto.AccountCryptor,
	storageService *storage.StorageService,
) *ForwardService {
	return &ForwardService{
		adapterManager:        adapterManager,
		contactRepository:     contactRepository,
		messageRepository:     messageRepository,
		messageSendService:    messageSendService,
		fileRepository:        fileRepository,
		accountRepository:     accountRepository,
		accountCryptorService: accountCryptorService,
		storageService:        storageService,
	}
}

func (f *ForwardService) ForwardMessages(ctx context.Context, forwardData *payloads.ForwardMessagesPayload) (response []*ForwardMessagesResponse, err error) {
	contacts, err := f.contactRepository.FindMany(ctx, repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
		return d.Where("id IN ?", forwardData.ContactsIds)
	}))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find contacts", slog.Any("error", err), slog.Any("forwardData", forwardData))
		return nil, fmt.Errorf("failed to find contacts: %w", err)
	}

	messages, err := f.messageRepository.FindMany(ctx, repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
		return d.Where("id IN ?", forwardData.MessagesIds).Order("timestamp asc")
	}))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find messages", slog.Any("error", err), slog.Any("forwardData", forwardData))
		return nil, fmt.Errorf("failed to find messages: %w", err)
	}

	for index, msg := range messages {
		file, err := f.fileRepository.FindOne(ctx, repositories.WithQueryStruct(map[string]interface{}{
			"attachedId": msg.Id,
			"accountId":  msg.AccountId,
		}))

		if err != nil && err.Error() != "record not found" {
			slog.ErrorContext(ctx, "Failed to find file", slog.Any("error", err), slog.Any("message", msg))
			return nil, fmt.Errorf("failed to find file: %w", err)
		}

		messages[index].File = file
	}

	for _, contact := range contacts {
		account, err := f.accountRepository.FindById(ctx, contact.AccountId)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to find account", slog.Any("error", err), slog.Any("contact", contact))
			return nil, fmt.Errorf("failed to find account: %w", err)
		}

		var sentMessagesIds []uuid.UUID

		for _, message := range messages {
			text, err := f.accountCryptorService.DecryptTextForAccount(ctx, account, message.Text, false)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to decrypt text", slog.Any("error", err), slog.Any("message", message), slog.Any("account", account))
				return nil, fmt.Errorf("failed to decrypt text: %w", err)
			}

			if text == "" {
				if message.Data.Location != nil {
					text = fmt.Sprintf(`https://www.google.com/maps?q=%v,%v`, message.Data.Location.Lat, message.Data.Location.Lng)
				} else {
					text = " "
				}
			}

			var fileId uuid.UUID

			if message.File != nil {
				stream, err := f.storageService.GetStream(ctx, message.File)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to get stream from file", slog.Any("error", err), slog.Any("file", message.File))
					return nil, fmt.Errorf("failed to get stream from file: %w", err)
				}

				size, bufferedStream, err := streamutils.GetStreamSizeBuffered(stream)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to get stream size", slog.Any("error", err), slog.Any("stream", stream))
					return nil, fmt.Errorf("failed to get stream size: %w", err)
				}

				checksum, err := common.GetChecksum(stream)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to get checksum", slog.Any("error", err), slog.Any("stream", stream))
					return nil, fmt.Errorf("failed to get checksum: %w", err)
				}

				file := &models.File{
					Name:         message.File.Name,
					AttachedType: "message.file",
					Mimetype:     message.File.Mimetype,
					Extension:    message.File.Extension,
					AccountId:    message.AccountId,
					Storage:      models.StorageType(utilsconfig.GetRandomBuckets()),
					Checksum:     checksum,
				}

				err = f.fileRepository.Create(ctx, file, nil)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to create file", slog.Any("error", err), slog.Any("file", file))
					return nil, fmt.Errorf("failed to create file: %w", err)
				}

				err = f.storageService.WriteStream(ctx, file, bufferedStream, &storage.WriteStreamOptions{
					ContentLength: size,
				})

				if err != nil {
					slog.ErrorContext(ctx, "Failed to write stream", slog.Any("error", err), slog.Any("file", file))
					return nil, fmt.Errorf("failed to write stream: %w", err)
				}

				fileId = file.Id
			}

			sendMessagePayload := &payloads.SendMessagePayload{
				ContactId:       contact.Id,
				Type:            message.Type,
				ServiceId:       contact.ServiceId,
				QuotedMessageId: message.QuotedMessageId,
				UserId:          forwardData.UserId,
				Origin:          "forward",
				AccountId:       message.AccountId,
				DontOpenTicket:  false,
				Text:            text,
				FileId:          fileId,
			}

			sentMessage, err := f.messageSendService.SendMessage(ctx, sendMessagePayload)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to send message", slog.Any("error", err), slog.Any("payload", sendMessagePayload))
				return nil, fmt.Errorf("failed to send message: %w", err)
			}

			sentMessagesIds = append(sentMessagesIds, sentMessage.Id)
		}

		response = append(response, &ForwardMessagesResponse{
			Id:          contact.Id,
			MessagesIds: sentMessagesIds,
		})
	}

	return response, nil
}
