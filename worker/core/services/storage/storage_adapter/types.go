package storage_adapter

import (
	"context"
	"digisac-go/worker/config"
	"io"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type GetPresignedUrlOptions struct {
	Key                        string
	Expiration                 time.Duration
	ResponseContentDisposition string
}

type WriteOptions struct {
	Key           string
	Body          io.Reader
	ContentLength int64
}

type ReadResponse s3.GetObjectOutput        // @TODO: Colocar apenas o necessário
type CopyObjectResponse s3.CopyObjectOutput // @TODO: Colocar apenas o necessário

type ReadOptions struct {
	Key string
}

type WriteData interface{}

type WriteResponse s3.PutObjectOutput // @TODO: Colocar apenas o necessário

type AdapterInterface interface {
	Connect(ctx context.Context) error                                                    // Conecta o cliente
	GetPresignedUrl(ctx context.Context, options *GetPresignedUrlOptions) (string, error) // Pega url assinada da midia
	Read(ctx context.Context, options *ReadOptions) (*ReadResponse, error)
	Write(ctx context.Context, options *WriteOptions) (*WriteResponse, error)
	Clone(ctx context.Context, sourceKey string, destinationKey string) (*CopyObjectResponse, error) // Clona um arquivo de um caminho para outro
}

type Adapter struct {
	AdapterInterface
	Storage string
	config  *config.Config
}
