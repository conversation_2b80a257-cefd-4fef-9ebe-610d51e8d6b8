package storage_adapter

import (
	"context"
	"fmt"
	"log/slog"

	configApp "digisac-go/worker/config"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type S3Adapter struct {
	client     *s3.Client
	bucketName string
	config     *configApp.Config
}

func (a *S3Adapter) Connect(ctx context.Context) error {
	region := a.config.AwsRegion
	accessKeyId := a.config.AwsAccessKeyId
	secretAccessKey := a.config.AwsSecretAccessKey
	a.bucketName = a.config.AwsBucketName

	if region == "" || accessKeyId == "" || secretAccessKey == "" || a.bucketName == "" {
		return fmt.Errorf("missing AWS configuration in .env")
	}

	creds := credentials.NewStaticCredentialsProvider(accessKeyId, secretAccessKey, "")

	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(region),
		config.WithCredentialsProvider(creds),
	)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to load AWS configuration", slog.String("error", err.Error()))
		return fmt.Errorf("failed to load AWS configuration: %w", err)
	}

	a.client = s3.NewFromConfig(cfg)

	return nil
}

func (a *S3Adapter) GetPresignedUrl(ctx context.Context, options *GetPresignedUrlOptions) (string, error) {
	req := &s3.GetObjectInput{
		Bucket:                     aws.String(a.bucketName),
		Key:                        aws.String(options.Key),
		ResponseContentDisposition: aws.String(options.ResponseContentDisposition),
	}

	presignClient := s3.NewPresignClient(a.client)

	signedURL, err := presignClient.PresignGetObject(ctx, req, s3.WithPresignExpires(options.Expiration))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to generate presigned URL", slog.String("key", options.Key), slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	return signedURL.URL, nil
}

func (a *S3Adapter) Write(ctx context.Context, options *WriteOptions) (*WriteResponse, error) {
	response, err := a.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:        aws.String(a.bucketName),
		Key:           aws.String(options.Key),
		Body:          options.Body,
		ContentLength: &options.ContentLength,
		Metadata: map[string]string{
			"Content-Length": fmt.Sprintf("%v", options.ContentLength),
		},
	})
	if err != nil {
		slog.ErrorContext(ctx, "Failed to write object to S3",
			slog.String("key", options.Key),
			slog.Int64("contentLength", options.ContentLength),
			slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to write object to S3: %w", err)
	}

	return &WriteResponse{
		ResultMetadata: response.ResultMetadata,
	}, nil
}

func (a *S3Adapter) Read(ctx context.Context, options *ReadOptions) (*ReadResponse, error) {
	resp, err := a.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(a.bucketName),
		Key:    aws.String(options.Key),
	})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to read object from S3", slog.String("key", options.Key), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to read object from S3: %w", err)
	}

	return &ReadResponse{
		Body: resp.Body,
	}, nil
}

func (a *S3Adapter) Clone(ctx context.Context, sourceKey string, destinationKey string) (*CopyObjectResponse, error) {
	resp, err := a.client.CopyObject(ctx, &s3.CopyObjectInput{
		Bucket:     aws.String(a.bucketName),
		CopySource: aws.String(a.bucketName + "/" + sourceKey),
		Key:        aws.String(destinationKey),
	})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to clone object in S3",
			slog.String("sourceKey", sourceKey),
			slog.String("destinationKey", destinationKey),
			slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to clone object in S3: %w", err)
	}

	return &CopyObjectResponse{
		ResultMetadata:   resp.ResultMetadata,
		CopyObjectResult: resp.CopyObjectResult,
	}, nil
}
