package ticket

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	queueDispatcher "digisac-go/worker/core/services/dispatcher/queue"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
	"gorm.io/gorm"
)

func NewTicketTransferService(
	ticketOpenService *TicketOpenService,
	ticketRepository repositories.TicketRepository,
	messageRepository repositories.MessageRepository,
	ticketTransferRepository repositories.TicketTransferRepository,
	contactRepository repositories.ContactRepository,
	userRepository repositories.UserRepository,
	metrics *Metrics,
	queueDispatcher *queueDispatcher.QueueJobsDispatcherService,
) *TicketTransferService {
	return &TicketTransferService{
		ticketOpenService:        ticketOpenService,
		ticketRepository:         ticketRepository,
		messageRepository:        messageRepository,
		ticketTransferRepository: ticketTransferRepository,
		contactRepository:        contactRepository,
		userRepository:           userRepository,
		metrics:                  metrics,
		queueDispatcher:          queueDispatcher,
	}
}

func (s *TicketTransferService) TransferTicket(
	ctx context.Context,
	data *TransferData,
	tx *gormrepository.Tx,
) (*models.Contact, error) {
	contact := data.Contact
	userId := data.UserId
	departmentId := data.DepartmentId
	byUserId := data.ByUserId
	comments := data.Comments
	transferredByBot := data.TransferredByBot
	transferredByDistribution := data.TransferredByDistribution

	currentTicket := contact.CurrentTicket

	if currentTicket == nil {
		ticketResult, err := s.ticketOpenService.OpenTicket(ctx, &OpenData{
			Contact:      contact,
			DepartmentId: departmentId,
			UserId:       userId,
			ByUserId:     byUserId,
			Comments:     comments,
			Origin:       "manual",
		}, tx)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to open ticket during transfer", slog.Any("data", data), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to open ticket during transfer: %w", err)
		}

		return ticketResult.UpdatedContact, nil
	}

	if currentTicket.DepartmentId == departmentId && currentTicket.UserId == userId {
		return contact, nil
	}

	var err error

	if tx == nil {
		tx = s.messageRepository.BeginTransaction()
		defer tx.Finish(&err)
	}

	now := time.Now()

	if currentTicket.CurrentTicketTransfer == nil {
		err := errors.New("currentTicket.currentTicketTransfer is required")
		slog.ErrorContext(ctx, "currentTicket.currentTicketTransfer is required", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, err
	}

	lastTicketTransfer := currentTicket.CurrentTicketTransfer

	if lastTicketTransfer.FirstMessageId != uuid.Nil {
		lastTicketTransfer.FirstMessage, err = s.messageRepository.FindById(ctx, lastTicketTransfer.FirstMessageId, repositories.WithTx(tx))
		if err != nil {
			slog.ErrorContext(ctx, "Failed to find first message by id", slog.String("messageId", lastTicketTransfer.FirstMessageId.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to find first message by id: %w", err)
		}
	}

	waitingTime, err := s.metrics.GetWaitingTime(ctx, lastTicketTransfer, currentTicket, tx)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to get waiting time", slog.String("ticketTransferId", lastTicketTransfer.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get waiting time: %w", err)
	}

	ticketTime := s.metrics.GetTicketTime(ctx, lastTicketTransfer, &now)

	lastTicketTransfer.EndedAt = &now
	lastTicketTransfer.Metrics.TicketTime = ticketTime
	lastTicketTransfer.Metrics.WaitingTime = waitingTime
	lastTicketTransfer.Metrics.TransferredByBot = transferredByBot

	err = s.ticketTransferRepository.UpdateById(ctx, lastTicketTransfer.Id, lastTicketTransfer, repositories.WithTx(tx))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to update last ticket transfer", slog.String("ticketTransferId", lastTicketTransfer.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update last ticket transfer: %w", err)
	}

	err = s.updateTicketMetrics(ctx, currentTicket, lastTicketTransfer, transferredByBot, tx)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to update ticket metrics", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update ticket metrics: %w", err)
	}

	ticketTransferMessage := &models.Message{
		Timestamp: &now,
		Type:      "ticket",
		Origin:    "ticket",
		Data: &models.MessageData{
			TicketTransfer: true,
		},
		TicketId:  currentTicket.Id,
		ContactId: contact.Id,
		ServiceId: contact.ServiceId,
		AccountId: contact.AccountId,
		Contact:   contact,
	}

	err = s.messageRepository.Create(ctx, ticketTransferMessage, repositories.WithTx(tx))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to create ticket transfer message", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create ticket transfer message: %w", err)
	}

	if ticketTransferMessage.Id == uuid.Nil {
		err := fmt.Errorf("falha ao obter o Id da mensagem após a criação")
		slog.ErrorContext(ctx, "Failed to get message id after creation", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, err
	}

	newTicketTransfer := &models.TicketTransfer{
		Action:               "transferred",
		AccountId:            contact.AccountId,
		ToDepartmentId:       departmentId,
		FromDepartmentId:     currentTicket.DepartmentId,
		ToUserId:             userId,
		FromUserId:           currentTicket.UserId,
		TransferredMessageId: ticketTransferMessage.Id,
		TicketId:             currentTicket.Id,
		ByUserId:             byUserId,
		Comments:             comments,
		StartedAt:            &now,
		FromDistribution:     transferredByDistribution,
	}
	err = s.ticketTransferRepository.Create(ctx, newTicketTransfer, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create new ticket transfer", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create new ticket transfer: %w", err)
	}

	err = s.queueDispatcher.Dispatch(
		ctx,
		"queued-start-summary",
		map[string]interface{}{
			"currentTicket": currentTicket,
			"type":          "transfer",
			"contact":       contact,
		},
		&queueDispatcher.QueueJobsDispatcherDispatchOptions{
			HashKey: contact.AccountId.String() + "_" + contact.ServiceId.String() + "_" + currentTicket.Id.String(),
		},
	)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch queued-start-summary", slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to dispatch queued-start-summary: %w", err)
	}

	var oldUser *models.User

	if currentTicket.UserId != uuid.Nil {
		oldUser, err = s.userRepository.FindById(ctx, currentTicket.UserId, repositories.WithTx(tx))
		if err != nil {
			slog.ErrorContext(ctx, "Failed to find old user by id", slog.String("userId", currentTicket.UserId.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to find old user by id: %w", err)
		}
	}

	currentTicket.DepartmentId = departmentId
	currentTicket.UserId = userId
	currentTicket.CurrentTicketTransferId = newTicketTransfer.Id

	err = s.ticketRepository.UpdateById(ctx, currentTicket.Id, currentTicket, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update current ticket", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update current ticket: %w", err)
	}

	if oldUser != nil && byUserId != uuid.Nil && !transferredByDistribution {
		// err := s.userRepository.EmitUserTransfer(oldUser)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to emit user transfer event", slog.String("userId", oldUser.Id.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to emit user transfer event: %w", err)
		}
	}

	contact.Unread = 1

	err = s.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update contact", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update contact: %w", err)
	}

	// err = s.ticketRepository.EmitTransfer(ctx, currentTicket)

	// if err != nil {
	// 	slog.ErrorContext(ctx, "Failed to emit ticket transfer event", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
	// 	return nil, fmt.Errorf("failed to emit ticket transfer event: %w", err)
	// }

	return contact, nil
}

func (s *TicketTransferService) updateTicketMetrics(ctx context.Context, ticket *models.Ticket, currentTicketTransfer *models.TicketTransfer, transferredByBot bool, tx *gormrepository.Tx) error {
	if transferredByBot {
		return nil
	}

	firstMessage, err := s.messageRepository.FindOne(ctx, repositories.WithTx(tx), repositories.WithQueryStruct(map[string]interface{}{
		"ticketId": ticket.Id,
		"visible":  true,
	}), repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Not("type = ?", "ticket").Order("timestamp asc")
	}))

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		slog.ErrorContext(ctx, "Failed to find first message", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
		return fmt.Errorf("failed to find first message for ticket %s: %w", ticket.Id, err)
	}

	isActiveTicket := firstMessage != nil && firstMessage.IsFromMe

	if isActiveTicket && !ticket.Metrics.IsActiveTicket {
		ticket.Metrics.IsActiveTicket = true
		if err := s.ticketRepository.UpdateById(ctx, ticket.Id, ticket, repositories.WithTx(tx)); err != nil {
			slog.ErrorContext(ctx, "Failed to update ticket isActiveTicket metric", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
			return fmt.Errorf("failed to update ticket %s isActiveTicket metric: %w", ticket.Id, err)
		}
	}

	ticketTransferCount := ticket.Metrics.TicketTransferCount
	waitingTimeTransfersSum := ticket.Metrics.WaitingTimeTransfersSum

	ticketTransferCount++
	waitingTimeTransfersSum += currentTicketTransfer.Metrics.WaitingTime
	waitingTimeTransfersAvg := (waitingTimeTransfersSum + ticketTransferCount - 1) / ticketTransferCount

	if waitingTimeTransfersAvg*ticketTransferCount != waitingTimeTransfersSum {
		slog.Warn("Possible difference in waitingTimeTransfersSum generated by metrics when transferring ticket")
	}
	if (waitingTimeTransfersSum+ticketTransferCount-1)/ticketTransferCount != waitingTimeTransfersAvg {
		slog.Warn("Possible difference in waitingTimeTransfersAvg generated by metrics when transferring ticket")
	}
	if waitingTimeTransfersSum/waitingTimeTransfersAvg != ticketTransferCount {
		slog.Warn("Possible difference in ticketTransferCount generated by metrics when transferring ticket")
	}

	ticket.Metrics.WaitingTimeTransfersSum = waitingTimeTransfersSum
	ticket.Metrics.WaitingTimeTransfersAvg = waitingTimeTransfersAvg
	ticket.Metrics.TicketTransferCount = ticketTransferCount

	if err := s.ticketRepository.UpdateById(ctx, ticket.Id, ticket, repositories.WithTx(tx)); err != nil {
		slog.ErrorContext(ctx, "Failed to update ticket metrics", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
		return fmt.Errorf("failed to update ticket %s metrics: %w", ticket.Id, err)
	}
	slog.DebugContext(ctx, "ticket metrics updated", slog.String("ticketId", ticket.Id.String()))

	return nil
}
