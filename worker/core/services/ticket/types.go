package ticket

import (
	"time"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	answer "digisac-go/worker/core/services/answer"
	"digisac-go/worker/core/services/crypto"
	queueDispatcher "digisac-go/worker/core/services/dispatcher/queue"
	"digisac-go/worker/core/utils/concurrency"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

type Metrics struct {
	serviceRepository repositories.ServiceRepository
	messageRepository repositories.MessageRepository
}

type TicketTransferService struct {
	ticketOpenService        *TicketOpenService
	ticketRepository         repositories.TicketRepository
	messageRepository        repositories.MessageRepository
	ticketTransferRepository repositories.TicketTransferRepository
	contactRepository        repositories.ContactRepository
	userRepository           repositories.UserRepository
	metrics                  *Metrics
	queueDispatcher          *queueDispatcher.QueueJobsDispatcherService
}

type CampaignData struct {
	DepartmentId uuid.UUID
	UserId       uuid.UUID
}

type TransferData struct {
	Contact                   *models.Contact
	UserId                    uuid.UUID
	DepartmentId              uuid.UUID
	ByUserId                  uuid.UUID
	Comments                  string
	Timestamp                 *time.Time
	TransferredByBot          bool
	TransferredByDistribution bool
}

type CloseData struct {
	Contact          *models.Contact
	ByUserId         uuid.UUID
	Comments         string
	TicketTopicIds   []uuid.UUID
	TransferredByBot bool
}

type OpenData struct {
	Contact      *models.Contact `validate:"required"`
	Message      *models.Message `validate:"required"`
	DepartmentId uuid.UUID       `validate:"required"`
	UserId       uuid.UUID
	ByUserId     uuid.UUID
	Origin       models.TicketOrigin `validate:"required"`
	StartedAt    *time.Time
	Comments     string
}

type AttachData struct {
	Contact *models.Contact
	Message *models.Message
}

type AttachResult struct {
	Ticket         *models.Ticket
	TicketTransfer *models.TicketTransfer
	Message        *models.Message
}

type TicketService struct {
	messageRepository        repositories.MessageRepository
	serviceRepository        repositories.ServiceRepository
	ticketRepository         repositories.TicketRepository
	ticketTransferRepository repositories.TicketTransferRepository
	TicketOpenService        *TicketOpenService
	TicketCloseService       *TicketCloseService
	TicketTransferService    *TicketTransferService
	metrics                  *Metrics
	accountCryptor           *crypto.AccountCryptor
}

type OpenTicketResult struct {
	Message           *models.Message
	Ticket            *models.Ticket
	TicketOpenMessage *models.Message
	UpdatedContact    *models.Contact
	TicketTransfer    *models.TicketTransfer
}

type TicketOpenService struct {
	redisClient              *redis.Client
	distributedLockFactory   concurrency.DistributedLockFactory
	validator                *validator.Validate
	accountRepository        repositories.AccountRepository
	ticketRepository         repositories.TicketRepository
	messageRepository        repositories.MessageRepository
	userRepository           repositories.UserRepository
	ticketTransferRepository repositories.TicketTransferRepository
	contactRepository        repositories.ContactRepository
	answerService            *answer.AnswerService
}

type TicketCloseService struct {
	ticketRepository         repositories.TicketRepository
	messageRepository        repositories.MessageRepository
	ticketTransferRepository repositories.TicketTransferRepository
	contactRepository        repositories.ContactRepository
	metrics                  *Metrics
	queueDispatcher          *queueDispatcher.QueueJobsDispatcherService
}
