package ticket

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/crypto"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
)

func NewTicketService(
	messageRepository repositories.MessageRepository,
	serviceRepository repositories.ServiceRepository,
	ticketRepository repositories.TicketRepository,
	ticketTransferRepository repositories.TicketTransferRepository,
	ticketOpenService *TicketOpenService,
	ticketCloseService *TicketCloseService,
	ticketTransferService *TicketTransferService,
	metrics *Metrics,
	accountCryptor *crypto.AccountCryptor,
) *TicketService {
	return &TicketService{
		messageRepository:        messageRepository,
		serviceRepository:        serviceRepository,
		ticketRepository:         ticketRepository,
		ticketTransferRepository: ticketTransferRepository,
		TicketOpenService:        ticketOpenService,
		TicketCloseService:       ticketCloseService,
		TicketTransferService:    ticketTransferService,
		metrics:                  metrics,
		accountCryptor:           accountCryptor,
	}
}

func (s *TicketService) GetMessageWithRelations(ctx context.Context, Id uuid.UUID, tx *gormrepository.Tx) (message *models.Message, err error) {
	if tx == nil {
		tx = s.messageRepository.BeginTransaction()
		defer tx.Finish(&err)
	}

	// Quebrar query em 2 pois o GORM quebra a saida do CurrentTicketTransfer.FirstMessage por ter joins muito grande
	message, err = s.messageRepository.FindOne(ctx,
		repositories.WithQueryStruct(map[string]interface{}{
			"id": Id,
		}), repositories.WithTx(tx), gormrepository.WithRelations("Contact", "Contact.Account", "Contact.Service"))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find message by id", slog.String("messageId", Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to find message by id: %w", err)
	}

	if message.Contact.CurrentTicketId != uuid.Nil {
		message.Contact.CurrentTicket, err = s.ticketRepository.FindOne(ctx,
			repositories.WithQueryStruct(map[string]interface{}{
				"id": message.Contact.CurrentTicketId,
			}),
			repositories.WithTx(tx), gormrepository.WithRelations("FirstMessage", "CurrentTicketTransfer", "CurrentTicketTransfer.FirstMessage"))

		if err != nil {
			slog.ErrorContext(ctx, "Failed to find ticket by id", slog.String("ticketId", message.Contact.CurrentTicketId.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to find ticket by id: %w", err)
		}
	}

	return message, err
}

func (s *TicketService) HandleMessageCreated(ctx context.Context, message *models.Message, campaignData *CampaignData, tx *gormrepository.Tx) (err error) {
	if message.Type == "ticket" {
		return nil
	}

	if tx == nil {
		tx = s.messageRepository.BeginTransaction()
		defer tx.Finish(&err)
	}
	messageWithRelations, err := s.GetMessageWithRelations(ctx, message.Id, tx)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to get message with relations", slog.String("messageId", message.Id.String()), slog.String("error", err.Error()))
		return fmt.Errorf("failed to get message with relations: %w", err)
	}

	if messageWithRelations == nil {
		err := errors.New("messageWithRelations is null. Outside transaction")
		slog.ErrorContext(ctx, "messageWithRelations is null", slog.String("error", err.Error()))
		return fmt.Errorf("messageWithRelations is null: %w", err)
	}

	return s.HandleTicket(ctx, messageWithRelations, campaignData, tx)
}

func (s *TicketService) TransferTicket(ctx context.Context, data *TransferData, tx *gormrepository.Tx) error {
	_, err := s.TicketTransferService.TransferTicket(ctx, data, tx)
	if err != nil {
		slog.ErrorContext(ctx, fmt.Sprintf("Error to transferTicket for contact #%s", data.Contact.Id)+err.Error(), slog.String("contactId", data.Contact.Id.String()), slog.String("error", err.Error()))
		return fmt.Errorf("failed to transfer ticket: %w", err)
	}
	return nil
}

func (s *TicketService) CloseTicket(ctx context.Context, data *CloseData, tx *gormrepository.Tx) error {
	_, err := s.TicketCloseService.CloseTicket(ctx, data, tx)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to close ticket", slog.Any("data", data), slog.String("error", err.Error()))
		return fmt.Errorf("failed to close ticket: %w", err)
	}
	return nil
}

func (s *TicketService) AttachToExisting(ctx context.Context, data *AttachData, tx *gormrepository.Tx) (*AttachResult, error) {
	contact := data.Contact
	message := data.Message
	currentTicket := contact.CurrentTicket

	if currentTicket.FirstMessage == nil {
		currentTicket.FirstMessage = message
	}

	if currentTicket.CurrentTicketTransfer == nil {
		err := errors.New("currentTicket.currentTicketTransfer is required")
		slog.ErrorContext(ctx, "currentTicket.currentTicketTransfer is required", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("currentTicket.currentTicketTransfer is required: %w", err)
	}

	currentTicketTransfer := currentTicket.CurrentTicketTransfer
	firstTicketTransferMessage := currentTicketTransfer.FirstMessage
	if firstTicketTransferMessage == nil {
		firstTicketTransferMessage = message
	}

	currentTicketTransfer.FirstMessageId = firstTicketTransferMessage.Id
	currentTicketTransfer.LastMessageId = message.Id

	err := s.ticketTransferRepository.UpdateById(ctx, currentTicketTransfer.Id, currentTicketTransfer, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update ticket transfer", slog.String("ticketTransferId", currentTicketTransfer.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update ticket transfer: %w", err)
	}

	currentTicket.LastMessageId = message.Id

	metrics, err := s.metrics.CalculatePerMessageMetrics(ctx, currentTicket.Metrics, message, currentTicket.FirstMessage, contact, tx)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to calculate per message metrics", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to calculate per message metrics: %w", err)
	}

	currentTicket.Metrics = metrics

	err = s.ticketRepository.UpdateById(ctx, currentTicket.Id, currentTicket, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update ticket", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update ticket: %w", err)
	}

	message.TicketId = contact.CurrentTicketId
	message.TicketUserId = currentTicket.UserId
	message.TicketDepartmentId = currentTicket.DepartmentId

	err = s.messageRepository.UpdateById(ctx, message.Id, message, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update message", slog.String("messageId", message.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update message: %w", err)
	}

	return &AttachResult{
		Ticket:         currentTicket,
		TicketTransfer: currentTicketTransfer,
		Message:        message,
	}, nil
}

func (s *TicketService) HandleTicket(ctx context.Context, message *models.Message, campaignData *CampaignData, tx *gormrepository.Tx) error {
	contact := message.Contact

	if message.Type == "ticket" {
		return nil
	}

	if contact == nil {
		err := errors.New("message.contact is required")
		slog.ErrorContext(ctx, "message.contact is required", slog.String("error", err.Error()))
		return fmt.Errorf("message.contact is required: %w", err)
	}

	currentTicket := contact.CurrentTicket
	service := contact.Service
	account := contact.Account

	if account == nil {
		err := errors.New("message.contact.account is required")
		slog.ErrorContext(ctx, "message.contact.account is required", slog.String("error", err.Error()))
		return fmt.Errorf("message.contact.account is required: %w", err)
	}

	if service == nil {
		err := errors.New("message.contact.service is required")
		slog.ErrorContext(ctx, "message.contact.service is required", slog.String("error", err.Error()))
		return fmt.Errorf("message.contact.service is required: %w", err)
	}

	ticketsEnabled := account.Settings.TicketsEnabled

	if !ticketsEnabled {
		return nil
	}

	defaultDepartmentId := account.DefaultDepartmentId
	if !account.Settings.DisableDefaultTicketTransfer {
		if contact.DefaultDepartmentId != uuid.Nil {
			defaultDepartmentId = contact.DefaultDepartmentId
		} else if service.DefaultDepartmentId != uuid.Nil {
			defaultDepartmentId = service.DefaultDepartmentId
		} else if account.DefaultDepartmentId != uuid.Nil {
			defaultDepartmentId = account.DefaultDepartmentId
		}
	}

	var userId uuid.UUID
	if campaignData != nil && campaignData.UserId != uuid.Nil {
		userId = campaignData.UserId
	} else if !account.Settings.DisableDefaultTicketTransfer {
		userId = contact.DefaultUserId
	}

	isOnOpeningWindow := message.Timestamp.After(time.Now().AddDate(0, 0, -7))
	isNew := message.Data.IsNew
	isFromFirstSync := message.Data.IsFromFirstSync
	dontOpenTicket := message.Data.DontOpenTicket

	isAFreshMessage := (isNew || (message.IsFromSync && !isFromFirstSync)) && isOnOpeningWindow

	var startDate *time.Time

	if currentTicket != nil {
		startDate = currentTicket.StartedAt
		if currentTicket.FirstMessage != nil {
			startDate = currentTicket.FirstMessage.Timestamp
		}
	}

	isAfterStartDate := false

	if startDate != nil && !startDate.IsZero() && message.Timestamp != nil && !message.Timestamp.IsZero() {
		isAfterStartDate = message.Timestamp.After(*startDate) || message.Timestamp.Equal(*startDate)
	}

	isPartOfATicket := !isFromFirstSync && isAfterStartDate

	shouldOpenTicketForGroups := service.Settings.ShouldOpenTicketForGroups

	isGroup := contact.IsGroup
	openForGroup := true
	if isGroup {
		openForGroup = shouldOpenTicketForGroups
	}

	shouldOpen := !dontOpenTicket &&
		isAFreshMessage &&
		contact.CurrentTicketId == uuid.Nil &&
		defaultDepartmentId != uuid.Nil &&
		message.Origin != "bot" &&
		message.Origin != "campaign" &&
		!contact.IsBroadcast &&
		openForGroup

	if shouldOpen || (campaignData != nil && contact.CurrentTicketId == uuid.Nil) {
		slog.DebugContext(ctx, "Opening ticket",
			"contact.id", contact.Id,
			"contact.name", contact.Name,
		)

		openDepartmentId := defaultDepartmentId

		if campaignData != nil && campaignData.DepartmentId != uuid.Nil {
			openDepartmentId = campaignData.DepartmentId
		}

		_, err := s.TicketOpenService.OpenTicket(ctx, &OpenData{
			Contact:      contact,
			DepartmentId: openDepartmentId,
			UserId:       userId,
			Message:      message,
			Origin:       "automatic",
		}, tx)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to open ticket", slog.Any("data", &OpenData{
				Contact:      contact,
				DepartmentId: openDepartmentId,
				UserId:       userId,
				Message:      message,
				Origin:       "automatic",
			}), slog.String("error", err.Error()))
			return fmt.Errorf("failed to open ticket: %w", err)
		}

		return nil
	}

	shouldAttachToExisting := contact.CurrentTicketId != uuid.Nil &&
		message.TicketId == uuid.Nil &&
		isPartOfATicket

	if shouldAttachToExisting {
		_, err := s.AttachToExisting(ctx, &AttachData{Contact: contact, Message: message}, tx)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to attach to existing ticket", slog.Any("data", &AttachData{Contact: contact, Message: message}), slog.String("error", err.Error()))
			return fmt.Errorf("failed to attach to existing ticket: %w", err)
		}
	}

	return nil
}
