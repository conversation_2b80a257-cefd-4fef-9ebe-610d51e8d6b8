package person

import (
	"context"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/utils/contact_utils"
	"fmt"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
	"gorm.io/gorm"
)

type PersonService struct {
	personRepository  repositories.PersonRepository
	contactRepository repositories.ContactRepository
	serviceRepository repositories.ServiceRepository
}

func NewPersonService(
	personRepository repositories.PersonRepository,
	contactRepository repositories.ContactRepository,
	serviceRepository repositories.ServiceRepository,
) *PersonService {
	return &PersonService{
		personRepository:  personRepository,
		contactRepository: contactRepository,
		serviceRepository: serviceRepository,
	}
}

func (s *PersonService) LinkContactToPerson(ctx context.Context, contactId uuid.UUID, tx *gormrepository.Tx) error {
	contact, err := s.contactRepository.FindById(ctx, contactId, repositories.WithTx(tx), repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
		return d.Joins("Service")
	}))

	if err != nil {
		return fmt.Errorf("failed to find contact: %w", err)
	}

	if contact.PersonId != uuid.Nil {
		return nil
	}

	idFromServiceVariants := contact_utils.GenerateIdFromServiceVariants(contact.IdFromService, contact.Service.Type)

	// Busca outro contato com a mesma variante do idFromService que tenha personId
	contactWithPerson, err := s.contactRepository.FindOne(
		ctx,
		repositories.WithTx(tx),
		repositories.WithQueryStruct(map[string]interface{}{
			"accountId":  contact.AccountId,
			"archivedAt": nil,
		}),
		repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
			return d.Where(`"idFromService" IN ?`, idFromServiceVariants)
		}),
	)

	if err != nil && err.Error() != "record not found" {
		return fmt.Errorf("failed to find contact with person: %w", err)
	}

	if contactWithPerson != nil {
		contact.PersonId = contactWithPerson.PersonId
		err = s.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))
		if err != nil {
			return fmt.Errorf("failed to update contact: %w", err)
		}
		return nil
	}

	return nil
}
