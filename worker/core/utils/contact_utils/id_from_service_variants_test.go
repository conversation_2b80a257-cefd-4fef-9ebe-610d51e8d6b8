//go:build unit
// +build unit

package contact_utils

import (
	"reflect"
	"testing"
)

func TestExtractDigitsFromString(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"abc+55 (11) 91234-5678", "5511912345678"},
		{"123abc456", "123456"},
		{"(99) 99999-9999", "99999999999"},
		{"", ""},
		{"sem numeros", ""},
	}

	for _, tt := range tests {
		result := ExtractDigitsFromString(tt.input)
		if result != tt.expected {
			t.Errorf("ExtractDigitsFromString(%q) = %q; want %q", tt.input, result, tt.expected)
		}
	}
}

func TestGenerateIdFromServiceVariants_Whatsapp(t *testing.T) {
	tests := []struct {
		input    string
		expected []string
	}{
		{"<EMAIL>", []string{"<EMAIL>", "<EMAIL>"}},
		{"<EMAIL>", []string{"<EMAIL>", "<EMAIL>"}},
		{"551198765432", []string{"<EMAIL>", "<EMAIL>"}},
		{"<EMAIL>", []string{"<EMAIL>"}},
		{"", []string{}},
	}

	for _, tt := range tests {
		result := GenerateIdFromServiceVariants(tt.input, "whatsapp")
		if !reflect.DeepEqual(result, tt.expected) {
			t.Errorf("GenerateIdFromServiceVariants(%q, whatsapp) = %v; want %v", tt.input, result, tt.expected)
		}
	}
}

func TestGenerateIdFromServiceVariants_SMSWavy(t *testing.T) {
	tests := []struct {
		input    string
		expected []string
	}{
		{"55***********", []string{"55***********", "551198765432"}},
		{"551198765432", []string{"55***********", "551198765432"}},
		{"(11) 99876-5432", []string{"***********"}},
		{"", []string{}},
	}

	for _, tt := range tests {
		result := GenerateIdFromServiceVariants(tt.input, "sms-wavy")
		if !reflect.DeepEqual(result, tt.expected) {
			t.Errorf("GenerateIdFromServiceVariants(%q, sms-wavy) = %v; want %v", tt.input, result, tt.expected)
		}
	}
}

func TestGenerateIdFromServiceVariants_WhatsappBusiness(t *testing.T) {
	tests := []struct {
		input    string
		expected []string
	}{
		{"55***********", []string{"55***********", "551198765432"}},
		{"551198765432", []string{"55***********", "551198765432"}},
		{"(11) 99876-5432", []string{"***********"}},
		{"abc", []string{""}}, // não bate regex, retorna string vazia extraída
		{"", []string{}},
	}

	for _, tt := range tests {
		result := GenerateIdFromServiceVariants(tt.input, "whatsapp-business")
		if !reflect.DeepEqual(result, tt.expected) {
			t.Errorf("GenerateIdFromServiceVariants(%q, whatsapp-business) = %v; want %v", tt.input, result, tt.expected)
		}
	}
}

func TestGenerateIdFromServiceVariants_WhatsappServerVariations(t *testing.T) {
	// Testa server diferente de c.us
	input := "<EMAIL>"
	expected := []string{"<EMAIL>", "<EMAIL>"}
	result := GenerateIdFromServiceVariants(input, "whatsapp")
	if !reflect.DeepEqual(result, expected) {
		t.Errorf("GenerateIdFromServiceVariants(%q, whatsapp) = %v; want %v", input, result, expected)
	}
}

func TestGenerateIdFromServiceVariants_WhatsappNoAt(t *testing.T) {
	// Testa idFromService sem @
	input := "551198765432"
	expected := []string{"<EMAIL>", "<EMAIL>"}
	result := GenerateIdFromServiceVariants(input, "whatsapp")
	if !reflect.DeepEqual(result, expected) {
		t.Errorf("GenerateIdFromServiceVariants(%q, whatsapp) = %v; want %v", input, result, expected)
	}
}

func TestGenerateIdFromServiceVariants_WhatsappNoRegexMatch(t *testing.T) {
	// Testa idFromService que não bate regex
	input := "12345"
	expected := []string{"<EMAIL>"}
	result := GenerateIdFromServiceVariants(input, "whatsapp")
	if !reflect.DeepEqual(result, expected) {
		t.Errorf("GenerateIdFromServiceVariants(%q, whatsapp) = %v; want %v", input, result, expected)
	}
}

func TestGenerateIdFromServiceVariants_DefaultEmpty(t *testing.T) {
	input := ""
	expected := []string{}
	result := GenerateIdFromServiceVariants(input, "outro-servico")
	if !reflect.DeepEqual(result, expected) {
		t.Errorf("GenerateIdFromServiceVariants(%q, outro-servico) = %v; want %v", input, result, expected)
	}
}

func TestGenerateIdFromServiceVariants_DefaultNotEmpty(t *testing.T) {
	input := "valor_qualquer"
	expected := []string{"valor_qualquer"}
	result := GenerateIdFromServiceVariants(input, "servico-desconhecido")
	if !reflect.DeepEqual(result, expected) {
		t.Errorf("GenerateIdFromServiceVariants(%q, servico-desconhecido) = %v; want %v", input, result, expected)
	}
}
