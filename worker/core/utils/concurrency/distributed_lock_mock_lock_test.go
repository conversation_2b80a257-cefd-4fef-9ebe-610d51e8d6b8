//go:build unit
// +build unit

package concurrency_test

import (
	"context"
	"errors"
	"sync"
	"testing"
	"time"

	"digisac-go/worker/core/utils/concurrency"

	"github.com/stretchr/testify/suite"
)

// MockMutex é um mock para o redsync.Mutex que sempre falha ao adquirir o lock
type MockFailingMutex struct {
	lockErr   error
	unlockErr error
}

func (m *MockFailingMutex) Lock() error {
	return m.lockErr
}

func (m *MockFailingMutex) Unlock() (bool, error) {
	return false, m.unlockErr
}

// MockFailingDistributedLock é um mock para o DistributedLock que sempre falha ao adquirir o lock
type MockFailingDistributedLock struct {
	mutex     *MockFailingMutex
	timeout   time.Duration
	taskMutex sync.Mutex
}

// Run executa uma tarefa dentro do MockFailingDistributedLock
func (m *MockFailingDistributedLock) Run(ctx context.Context, task func() (interface{}, error)) (interface{}, error) {
	m.taskMutex.Lock()
	defer m.taskMutex.Unlock()

	// Tentar adquirir o lock (sempre falha)
	if err := m.mutex.Lock(); err != nil {
		return nil, err
	}

	// Nunca chega aqui, mas mantemos o código para completude
	defer func() {
		_, _ = m.mutex.Unlock()
	}()

	taskCh := make(chan struct{})
	timeoutCh := time.After(m.timeout)

	var result interface{}
	var err error

	go func() {
		result, err = task()
		close(taskCh)
	}()

	select {
	case <-taskCh:
		// Tarefa concluída com sucesso
	case <-timeoutCh:
		// Timeout atingido
		err = concurrency.ErrTimeoutDistributedLock
	case <-ctx.Done():
		// Contexto cancelado
		err = ctx.Err()
	}

	return result, err
}

// MockFailingDistributedLockFactory cria instâncias de MockFailingDistributedLock
func MockFailingDistributedLockFactory(lockErr, unlockErr error) func(key string, timeout time.Duration) concurrency.DistributedLock {
	return func(key string, timeout time.Duration) concurrency.DistributedLock {
		// Usar o timeout padrão de 60s se o timeout for 0
		if timeout == 0 {
			timeout = 60 * time.Second
		}
		return &MockFailingDistributedLock{
			mutex: &MockFailingMutex{
				lockErr:   lockErr,
				unlockErr: unlockErr,
			},
			timeout: timeout,
		}
	}
}

type MockFailingLockSuite struct {
	suite.Suite
	LockErrorFactory   func(key string, timeout time.Duration) concurrency.DistributedLock
	UnlockErrorFactory func(key string, timeout time.Duration) concurrency.DistributedLock
}

func (suite *MockFailingLockSuite) SetupSuite() {
	// Criar factory que falha no Lock
	suite.LockErrorFactory = MockFailingDistributedLockFactory(errors.New("lock acquisition failed"), nil)

	// Criar factory que falha no Unlock
	suite.UnlockErrorFactory = MockFailingDistributedLockFactory(nil, errors.New("unlock failed"))
}

func (suite *MockFailingLockSuite) TestLockAcquisitionFailure() {
	// Usar o factory que falha no Lock
	queue := suite.LockErrorFactory("failing-lock", 100*time.Millisecond)

	task := func() (interface{}, error) {
		return "should not execute", nil
	}

	// Executar a tarefa (deve falhar ao adquirir o lock)
	result, err := queue.Run(context.TODO(), task)

	suite.Require().Error(err)
	suite.Require().Nil(result)
	suite.Require().Equal("lock acquisition failed", err.Error())
}

func TestMockFailingLockSuite(t *testing.T) {
	suite.Run(t, new(MockFailingLockSuite))
}
