package common

import (
	"crypto/sha1"
	"encoding/hex"
	"io"
)

// GetChecksum calculates the SHA-1 checksum of data from an io.Reader
//
// Parameters:
//   - data: The io.Reader to calculate the checksum from
//
// Returns:
//   - string: The hex-encoded SHA-1 checksum
//   - error: Any error that occurred during calculation
func GetChecksum(data io.Reader) (string, error) {
	hasher := sha1.New()

	_, err := io.Copy(hasher, data)
	if err != nil {
		return "", err
	}

	return hex.EncodeToString(hasher.Sum(nil)), nil
}
