//go:build unit
// +build unit

package common

import (
	"testing"
)

func TestInterpolate(t *testing.T) {
	tests := []struct {
		name     string
		str      string
		data     map[string]any
		expected string
	}{
		// Basic variable substitution tests
		{
			name: "simple string substitution",
			str:  "{{foo}}-bar",
			data: map[string]any{
				"foo": "baz",
			},
			expected: "baz-bar",
		},
		{
			name: "multiple variables in string",
			str:  "{{first}} {{last}}",
			data: map[string]any{
				"first": "John",
				"last":  "Doe",
			},
			expected: "John Doe",
		},
		{
			name: "variable with spaces in name",
			str:  "{{ variable_with_spaces }}",
			data: map[string]any{
				"variable_with_spaces": "value",
			},
			expected: "value",
		},

		// Different data types tests
		{
			name: "boolean true value",
			str:  "{{foo}}",
			data: map[string]any{
				"foo": true,
			},
			expected: "true",
		},
		{
			name: "boolean false value",
			str:  "{{foo}}",
			data: map[string]any{
				"foo": false,
			},
			expected: "false",
		},
		{
			name: "integer value",
			str:  "{{number}}",
			data: map[string]any{
				"number": 42,
			},
			expected: "42",
		},
		{
			name: "float value",
			str:  "{{number}}",
			data: map[string]any{
				"number": 3.14,
			},
			expected: "3.14",
		},
		{
			name: "empty string value",
			str:  "{{foo}}",
			data: map[string]any{
				"foo": "",
			},
			expected: "",
		},
		{
			name: "nil value",
			str:  "{{foo}}",
			data: map[string]any{
				"foo": nil,
			},
			expected: "<nil>",
		},

		// Pad modifier tests
		{
			name: "pad modifier with number",
			str:  "{{count|pad:5}}",
			data: map[string]any{
				"count": 1,
			},
			expected: "00001",
		},
		{
			name: "pad modifier with string",
			str:  "{{text|pad:5}}",
			data: map[string]any{
				"text": "ab",
			},
			expected: "000ab",
		},
		{
			name: "pad modifier with custom character",
			str:  "{{count|pad:5,F}}",
			data: map[string]any{
				"count": 1,
			},
			expected: "FFFF1",
		},
		{
			name: "pad modifier with spaces in syntax",
			str:  "{{ count | pad : 5}}",
			data: map[string]any{
				"count": 1,
			},
			expected: "00001",
		},
		{
			name: "pad modifier with missing parameter",
			str:  "{{count|pad:}}",
			data: map[string]any{
				"count": 1,
			},
			expected: "1",
		},
		{
			name: "pad modifier with invalid parameter",
			str:  "{{count|pad:abc}}",
			data: map[string]any{
				"count": 1,
			},
			expected: "1",
		},
		{
			name: "pad modifier with value longer than padding",
			str:  "{{text|pad:2}}",
			data: map[string]any{
				"text": "abcde",
			},
			expected: "abcde",
		},

		// Case modifiers tests
		{
			name: "upper modifier with lowercase string",
			str:  "{{foo|upper}}",
			data: map[string]any{
				"foo": "bar",
			},
			expected: "BAR",
		},
		{
			name: "upper modifier with mixed case string",
			str:  "{{foo|upper}}",
			data: map[string]any{
				"foo": "BaR",
			},
			expected: "BAR",
		},
		{
			name: "upper modifier with number",
			str:  "{{num|upper}}",
			data: map[string]any{
				"num": 123,
			},
			expected: "123",
		},
		{
			name: "lower modifier with uppercase string",
			str:  "{{foo|lower}}",
			data: map[string]any{
				"foo": "BAR",
			},
			expected: "bar",
		},
		{
			name: "lower modifier with mixed case string",
			str:  "{{foo|lower}}",
			data: map[string]any{
				"foo": "BaR",
			},
			expected: "bar",
		},

		// Noop modifier tests
		{
			name: "explicit noop modifier",
			str:  "{{foo|noop}}",
			data: map[string]any{
				"foo": "bar",
			},
			expected: "bar",
		},
		{
			name: "non-existing modifier (falls back to noop)",
			str:  "{{count|nonexistent}}",
			data: map[string]any{
				"count": 1,
			},
			expected: "1",
		},

		// Multiple modifiers tests
		{
			name: "multiple modifiers - pad then upper",
			str:  "{{foo|pad:5|upper}}",
			data: map[string]any{
				"foo": "bar",
			},
			expected: "00BAR",
		},
		{
			name: "multiple modifiers - upper then pad",
			str:  "{{foo|upper|pad:5}}",
			data: map[string]any{
				"foo": "bar",
			},
			expected: "00BAR",
		},
		{
			name: "multiple modifiers - lower then pad with custom char",
			str:  "{{foo|lower|pad:5,*}}",
			data: map[string]any{
				"foo": "BAR",
			},
			expected: "**bar",
		},
		{
			name: "multiple modifiers with spaces",
			str:  "{{ foo | upper | pad : 5 , * }}",
			data: map[string]any{
				"foo": "bar",
			},
			expected: "**BAR",
		},

		// Real-world examples
		{
			name: "simple greeting",
			str:  "Olá {{contact_name}}, tudo bem?",
			data: map[string]any{
				"contact_name": "Daniel",
			},
			expected: "Olá Daniel, tudo bem?",
		},
		{
			name: "order confirmation with multiple variables",
			str:  "Seu pedido #{{order_id|pad:6}} no valor de R$ {{amount}} foi confirmado!",
			data: map[string]any{
				"order_id": 123,
				"amount":   "59,90",
			},
			expected: "Seu pedido #000123 no valor de R$ 59,90 foi confirmado!",
		},

		// Edge cases
		{
			name:     "missing variable",
			str:      "Olá {{contact_name}}, tudo bem?",
			data:     map[string]any{},
			expected: "Olá {{contact_name}}, tudo bem?",
		},
		{
			name:     "empty template string",
			str:      "",
			data:     map[string]any{"foo": "bar"},
			expected: "",
		},
		{
			name:     "template with no variables",
			str:      "Hello, world!",
			data:     map[string]any{"foo": "bar"},
			expected: "Hello, world!",
		},
		{
			name: "template with special characters",
			str:  "{{special}}",
			data: map[string]any{
				"special": "!@#$%^&*()",
			},
			expected: "!@#$%^&*()",
		},
		{
			name: "incomplete variable syntax",
			str:  "Hello {{name",
			data: map[string]any{
				"name": "John",
			},
			expected: "Hello {{name",
		},
		{
			name: "empty variable name",
			str:  "Hello {{}}",
			data: map[string]any{
				"name": "John",
			},
			expected: "Hello {{}}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := Interpolate(tt.str, tt.data)
			if actual != tt.expected {
				t.Errorf("expected %v, got %v", tt.expected, actual)
			}
		})
	}
}
