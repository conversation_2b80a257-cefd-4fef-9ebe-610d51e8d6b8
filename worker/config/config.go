package config

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"log/slog"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	WhatsappApiHttpAddress    string
	WhatsappApiGrpcAddress    string
	WhatsappApiToken          string
	WorkersGoPort             string
	ApiPublicUrl              string
	EncryptionKey             string
	ApiAddress                string
	LogType                   string
	DbHost                    string
	DbPort                    string
	DbLog                     bool
	DbUsername                string
	DbPassword                string
	DbName                    string
	DbMaxConns                int
	Deployment                string
	Branch                    string
	StorageDriver             string
	AwsRegion                 string
	AwsAccessKeyId            string
	AwsSecretAccessKey        string
	AwsBucketName             string
	OracleRegion              string
	OracleAccessKeyId         string
	OracleSecretAccessKey     string
	OracleBucketsNames        string
	OracleBucketNameFallback  string
	OracleEndpoint            string
	GupshupEmail              string
	GupshupPassword           string
	DefaultHsmLimit           int
	DisableWabaWebhookUrlSet  bool
	DriversGatewayUrl         string
	FacebookAppId             string
	QueueManagerDispatcherUrl string
	MockDriverUrl             string
	UseMockDriver             bool
}

var runtimeCaller = runtime.Caller

var getDotEnvPath = func(ctx context.Context) string {
	_, filename, _, ok := runtimeCaller(0)
	if !ok {
		err := errors.New("can not get path to dotenv file")
		slog.ErrorContext(ctx, fmt.Sprintf("Error: %v", err), slog.Any("filename", filename))
		panic(fmt.Errorf("can not get path to dotenv file: %w", err))
	}
	dirname := filepath.Dir(filename)

	file := "../.env"

	if flag.Lookup("test.v") != nil {
		file = "../.env.test"
	}

	return path.Join(dirname, file)
}

func Load(ctx context.Context) {
	err := godotenv.Load(getDotEnvPath(ctx))
	// Caso não encontre o arquivo /app/worker/.env usa as variáveis do sistema operacional.

	// Para usar /app/worker/.env é necessário um volume, exemplo:
	// # volumes:
	// #   - .env-worker-go-back:/app/worker/.env

	// Para usar direto as envs do SO:
	// env_file:
	//   - .env-worker-go-back

	if err != nil {
		slog.ErrorContext(ctx, "Error loading .env file, using OS envs", slog.Any("error", err))

		slog.Warn("##### /app/worker/.env IS MISSING! ####")
		slog.Warn("##### USING OS ENVS! ####")
	}
}

func GetWhatsappApiHttpAddress() string {
	return os.Getenv("WHATSAPP_API_HTTP_ADDRESS")
}

func GetWhatsappApiGrpcAddress() string {
	return os.Getenv("WHATSAPP_API_GRPC_ADDRESS")
}

func GetWhatsappApiToken() string {
	return os.Getenv("WHATSAPP_API_TOKEN")
}

func GetApiPublicUrl() string {
	return os.Getenv("PUBLIC_URL")
}

func GetEncryptionKey() string {
	return os.Getenv("ENCRYPTION_KEY")
}

func GetWorkersGoPort() string {
	return os.Getenv("WORKERS_GO_PORT")
}

func GetApiAddress() string {
	return ":" + GetWorkersGoPort()
}

func GetLogType() string {
	return os.Getenv("LOG_TYPE")
}

func GetDbHost() string {
	return os.Getenv("DB_HOST")
}

func GetDbPort() string {
	return os.Getenv("DB_PORT")
}

func GetDbLog() bool {
	return os.Getenv("DB_LOG") == "true"
}

func GetDbUsername() string {
	return os.Getenv("DB_USERNAME")
}

func GetDbPassword() string {
	return os.Getenv("DB_PASSWORD")
}

func GetDbName() string {
	return os.Getenv("DB_NAME")
}

func GetDbMaxConns(ctx context.Context) int {
	res, err := strconv.Atoi(os.Getenv("DB_MAX_CONNS"))
	if err != nil {
		slog.ErrorContext(ctx, fmt.Sprintf("Error converting DB_MAX_CONNS to integer, value: %v", os.Getenv("DB_MAX_CONNS")), slog.Any("error", err))
		panic(fmt.Errorf("DB_MAX_CONNS must be integer: %w", err))
	}

	if res <= 0 {
		slog.ErrorContext(ctx, "DB_MAX_CONNS must be a positive integer, using default value 10")
		return 10
	}

	return res
}

func GetDeployment() string {
	return os.Getenv("DEPLOYMENT")
}

func GetBranch() string {
	return os.Getenv("BRANCH")
}

func GetStorageDriver() string {
	return os.Getenv("STORAGE_DRIVER")
}

func GetAwsRegion() string {
	return os.Getenv("AWS_REGION")
}

func GetAwsAccessKeyId() string {
	return os.Getenv("AWS_ACCESS_KEY_ID")
}

func GetAwsSecretAccessKey() string {
	return os.Getenv("AWS_SECRET_ACCESS_KEY")
}

func GetAwsBucketName() string {
	return os.Getenv("AWS_BUCKET_NAME")
}

func GetOracleRegion() string {
	return os.Getenv("ORACLE_REGION")
}

func GetOracleAccessKeyId() string {
	return os.Getenv("ORACLE_ACCESS_KEY_ID")
}

func GetOracleSecretAccessKey() string {
	return os.Getenv("ORACLE_SECRET_ACCESS_KEY")
}

func GetOracleBucketsNames() string {
	return os.Getenv("ORACLE_BUCKETS_NAMES")
}

func GetOracleBucketNameFallback() string {
	return os.Getenv("ORACLE_BUCKET_NAME_FALLBACK")
}

func GetOracleEndpoint() string {
	return os.Getenv("ORACLE_ENDPOINT")
}

func GetGupshupEmail() string {
	return os.Getenv("GUPSHUP_EMAIL")
}

func GetGupshupPassword() string {
	return os.Getenv("GUPSHUP_PASSWORD")
}

func GetDefaultHsmLimit(ctx context.Context) int {
	limit, err := strconv.Atoi(os.Getenv("DEFAULT_HSM_LIMIT"))

	if err != nil || limit <= 0 {
		slog.ErrorContext(ctx, "DEFAULT_HSM_LIMIT must be a positive integer, using default value 3000", slog.Any("error", err))
		return 3000
	}

	return limit
}

func GetDisableWabaWebhookUrlSet() bool {
	return os.Getenv("DISABLE_WABA_WEBHOOK_URL_SET") == "true"
}

func GetDriversGatewayUrl() string {
	return os.Getenv("DRIVERS_GATEWAY_URL")
}

func GetFacebookAppId() string {
	return os.Getenv("FACEBOOK_APP_ID")
}

func GetQueueManagerDispatcherUrl() string {
	return os.Getenv("QUEUE_MANAGER_DISPATCHER_URL")
}

func GetMockDriverUrl() string {
	return os.Getenv("MOCK_DRIVER_URL")
}

func GetUseMockDriver() bool {
	return os.Getenv("USE_MOCK_DRIVER") == "true"
}

func NewConfig() (*Config, error) {
	ctx := context.Background()
	Load(ctx)

	return &Config{
		WhatsappApiHttpAddress:    GetWhatsappApiHttpAddress(),
		WhatsappApiGrpcAddress:    GetWhatsappApiGrpcAddress(),
		WhatsappApiToken:          GetWhatsappApiToken(),
		ApiPublicUrl:              GetApiPublicUrl(),
		EncryptionKey:             GetEncryptionKey(),
		WorkersGoPort:             GetWorkersGoPort(),
		ApiAddress:                GetApiAddress(),
		LogType:                   GetLogType(),
		DbHost:                    GetDbHost(),
		DbPort:                    GetDbPort(),
		DbLog:                     GetDbLog(),
		DbUsername:                GetDbUsername(),
		DbPassword:                GetDbPassword(),
		DbName:                    GetDbName(),
		DbMaxConns:                GetDbMaxConns(ctx),
		Deployment:                GetDeployment(),
		Branch:                    GetBranch(),
		StorageDriver:             GetStorageDriver(),
		AwsRegion:                 GetAwsRegion(),
		AwsAccessKeyId:            GetAwsAccessKeyId(),
		AwsSecretAccessKey:        GetAwsSecretAccessKey(),
		AwsBucketName:             GetAwsBucketName(),
		OracleRegion:              GetOracleRegion(),
		OracleAccessKeyId:         GetOracleAccessKeyId(),
		OracleSecretAccessKey:     GetOracleSecretAccessKey(),
		OracleBucketsNames:        GetOracleBucketsNames(),
		OracleBucketNameFallback:  GetOracleBucketNameFallback(),
		OracleEndpoint:            GetOracleEndpoint(),
		GupshupEmail:              GetGupshupEmail(),
		GupshupPassword:           GetGupshupPassword(),
		DefaultHsmLimit:           GetDefaultHsmLimit(ctx),
		DisableWabaWebhookUrlSet:  GetDisableWabaWebhookUrlSet(),
		DriversGatewayUrl:         GetDriversGatewayUrl(),
		FacebookAppId:             GetFacebookAppId(),
		QueueManagerDispatcherUrl: GetQueueManagerDispatcherUrl(),
		MockDriverUrl:             GetMockDriverUrl(),
		UseMockDriver:             GetUseMockDriver(),
	}, nil
}
