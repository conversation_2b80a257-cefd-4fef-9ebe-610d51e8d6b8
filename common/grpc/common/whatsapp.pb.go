// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v3.21.12
// source: common/grpc/common/proto/whatsapp.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActionPayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ServiceId     string                 `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionPayload) Reset() {
	*x = ActionPayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionPayload) ProtoMessage() {}

func (x *ActionPayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionPayload.ProtoReflect.Descriptor instead.
func (*ActionPayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{0}
}

func (x *ActionPayload) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

type ActionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionResponse) Reset() {
	*x = ActionResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionResponse) ProtoMessage() {}

func (x *ActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionResponse.ProtoReflect.Descriptor instead.
func (*ActionResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{1}
}

type Metadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ServiceId     string                 `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{2}
}

func (x *Metadata) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

type File struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Mimetype      string                 `protobuf:"bytes,2,opt,name=mimetype,proto3" json:"mimetype,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	AsSticker     bool                   `protobuf:"varint,4,opt,name=asSticker,proto3" json:"asSticker,omitempty"`
	AsDocument    bool                   `protobuf:"varint,5,opt,name=asDocument,proto3" json:"asDocument,omitempty"`
	AsPtt         bool                   `protobuf:"varint,6,opt,name=asPtt,proto3" json:"asPtt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *File) Reset() {
	*x = File{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File) ProtoMessage() {}

func (x *File) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File.ProtoReflect.Descriptor instead.
func (*File) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{3}
}

func (x *File) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *File) GetMimetype() string {
	if x != nil {
		return x.Mimetype
	}
	return ""
}

func (x *File) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *File) GetAsSticker() bool {
	if x != nil {
		return x.AsSticker
	}
	return false
}

func (x *File) GetAsDocument() bool {
	if x != nil {
		return x.AsDocument
	}
	return false
}

func (x *File) GetAsPtt() bool {
	if x != nil {
		return x.AsPtt
	}
	return false
}

// ****************************************
// SEND MESSAGE
// ****************************************
type SendMessagePayload struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ChatId          string                 `protobuf:"bytes,2,opt,name=chatId,proto3" json:"chatId,omitempty"`
	Text            string                 `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	QuotedMessageId string                 `protobuf:"bytes,4,opt,name=quotedMessageId,proto3" json:"quotedMessageId,omitempty"`
	File            *File                  `protobuf:"bytes,5,opt,name=file,proto3,oneof" json:"file,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SendMessagePayload) Reset() {
	*x = SendMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessagePayload) ProtoMessage() {}

func (x *SendMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessagePayload.ProtoReflect.Descriptor instead.
func (*SendMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{4}
}

func (x *SendMessagePayload) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SendMessagePayload) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *SendMessagePayload) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SendMessagePayload) GetQuotedMessageId() string {
	if x != nil {
		return x.QuotedMessageId
	}
	return ""
}

func (x *SendMessagePayload) GetFile() *File {
	if x != nil {
		return x.File
	}
	return nil
}

type SendMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *Metadata              `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *SendMessagePayload    `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{5}
}

func (x *SendMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendMessageRequest) GetPayload() *SendMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type SendMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageResponse) Reset() {
	*x = SendMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResponse) ProtoMessage() {}

func (x *SendMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResponse.ProtoReflect.Descriptor instead.
func (*SendMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{6}
}

func (x *SendMessageResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ****************************************
// REVOKE MESSAGE
// ****************************************
type RevokeMessagePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeMessagePayload) Reset() {
	*x = RevokeMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMessagePayload) ProtoMessage() {}

func (x *RevokeMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMessagePayload.ProtoReflect.Descriptor instead.
func (*RevokeMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{7}
}

func (x *RevokeMessagePayload) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type RevokeMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeMessageResponse) Reset() {
	*x = RevokeMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMessageResponse) ProtoMessage() {}

func (x *RevokeMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMessageResponse.ProtoReflect.Descriptor instead.
func (*RevokeMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{8}
}

func (x *RevokeMessageResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type RevokeMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *Metadata              `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *RevokeMessagePayload  `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeMessageRequest) Reset() {
	*x = RevokeMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMessageRequest) ProtoMessage() {}

func (x *RevokeMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMessageRequest.ProtoReflect.Descriptor instead.
func (*RevokeMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{9}
}

func (x *RevokeMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RevokeMessageRequest) GetPayload() *RevokeMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

// ****************************************
// FORWARD MESSAGE
// ****************************************
type ForwardMessagePayload struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ChatId           string                 `protobuf:"bytes,2,opt,name=chatId,proto3" json:"chatId,omitempty"`
	ForwardMessageId string                 `protobuf:"bytes,3,opt,name=forwardMessageId,proto3" json:"forwardMessageId,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ForwardMessagePayload) Reset() {
	*x = ForwardMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMessagePayload) ProtoMessage() {}

func (x *ForwardMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMessagePayload.ProtoReflect.Descriptor instead.
func (*ForwardMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{10}
}

func (x *ForwardMessagePayload) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ForwardMessagePayload) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *ForwardMessagePayload) GetForwardMessageId() string {
	if x != nil {
		return x.ForwardMessageId
	}
	return ""
}

type ForwardMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForwardMessageResponse) Reset() {
	*x = ForwardMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMessageResponse) ProtoMessage() {}

func (x *ForwardMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMessageResponse.ProtoReflect.Descriptor instead.
func (*ForwardMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{11}
}

func (x *ForwardMessageResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ForwardMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *Metadata              `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *ForwardMessagePayload `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForwardMessageRequest) Reset() {
	*x = ForwardMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMessageRequest) ProtoMessage() {}

func (x *ForwardMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMessageRequest.ProtoReflect.Descriptor instead.
func (*ForwardMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{12}
}

func (x *ForwardMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ForwardMessageRequest) GetPayload() *ForwardMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

// ****************************************
// SEND REACTION TO MESSAGE
// ****************************************
type SendReactionToMessagePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chatId,proto3" json:"chatId,omitempty"`
	MessageId     string                 `protobuf:"bytes,2,opt,name=messageId,proto3" json:"messageId,omitempty"`
	Reaction      string                 `protobuf:"bytes,3,opt,name=reaction,proto3" json:"reaction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendReactionToMessagePayload) Reset() {
	*x = SendReactionToMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendReactionToMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendReactionToMessagePayload) ProtoMessage() {}

func (x *SendReactionToMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendReactionToMessagePayload.ProtoReflect.Descriptor instead.
func (*SendReactionToMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{13}
}

func (x *SendReactionToMessagePayload) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *SendReactionToMessagePayload) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *SendReactionToMessagePayload) GetReaction() string {
	if x != nil {
		return x.Reaction
	}
	return ""
}

type SendReactionToMessageRequest struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Metadata      *Metadata                     `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *SendReactionToMessagePayload `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendReactionToMessageRequest) Reset() {
	*x = SendReactionToMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendReactionToMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendReactionToMessageRequest) ProtoMessage() {}

func (x *SendReactionToMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendReactionToMessageRequest.ProtoReflect.Descriptor instead.
func (*SendReactionToMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{14}
}

func (x *SendReactionToMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendReactionToMessageRequest) GetPayload() *SendReactionToMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type SendReactionToMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendReactionToMessageResponse) Reset() {
	*x = SendReactionToMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendReactionToMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendReactionToMessageResponse) ProtoMessage() {}

func (x *SendReactionToMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendReactionToMessageResponse.ProtoReflect.Descriptor instead.
func (*SendReactionToMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{15}
}

func (x *SendReactionToMessageResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ****************************************
// CREATE GROUP
// ****************************************
type CreateGroupPayload struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Name           string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ParticipantIds []string               `protobuf:"bytes,2,rep,name=participantIds,proto3" json:"participantIds,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateGroupPayload) Reset() {
	*x = CreateGroupPayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupPayload) ProtoMessage() {}

func (x *CreateGroupPayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupPayload.ProtoReflect.Descriptor instead.
func (*CreateGroupPayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{16}
}

func (x *CreateGroupPayload) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateGroupPayload) GetParticipantIds() []string {
	if x != nil {
		return x.ParticipantIds
	}
	return nil
}

type CreateGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *Metadata              `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *CreateGroupPayload    `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupRequest) Reset() {
	*x = CreateGroupRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupRequest) ProtoMessage() {}

func (x *CreateGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateGroupRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{17}
}

func (x *CreateGroupRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateGroupRequest) GetPayload() *CreateGroupPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type CreateGroupParticipantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ContactId     string                 `protobuf:"bytes,1,opt,name=contactId,proto3" json:"contactId,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Error         string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupParticipantResponse) Reset() {
	*x = CreateGroupParticipantResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupParticipantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupParticipantResponse) ProtoMessage() {}

func (x *CreateGroupParticipantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupParticipantResponse.ProtoReflect.Descriptor instead.
func (*CreateGroupParticipantResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{18}
}

func (x *CreateGroupParticipantResponse) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

func (x *CreateGroupParticipantResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateGroupParticipantResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type CreateGroupResponse struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Id            string                            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Participants  []*CreateGroupParticipantResponse `protobuf:"bytes,2,rep,name=participants,proto3" json:"participants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupResponse) Reset() {
	*x = CreateGroupResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupResponse) ProtoMessage() {}

func (x *CreateGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupResponse.ProtoReflect.Descriptor instead.
func (*CreateGroupResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{19}
}

func (x *CreateGroupResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreateGroupResponse) GetParticipants() []*CreateGroupParticipantResponse {
	if x != nil {
		return x.Participants
	}
	return nil
}

type LoadEarlierMessagesTillDatePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ContactId     string                 `protobuf:"bytes,1,opt,name=contactId,proto3" json:"contactId,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadEarlierMessagesTillDatePayload) Reset() {
	*x = LoadEarlierMessagesTillDatePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadEarlierMessagesTillDatePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadEarlierMessagesTillDatePayload) ProtoMessage() {}

func (x *LoadEarlierMessagesTillDatePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadEarlierMessagesTillDatePayload.ProtoReflect.Descriptor instead.
func (*LoadEarlierMessagesTillDatePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{20}
}

func (x *LoadEarlierMessagesTillDatePayload) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

func (x *LoadEarlierMessagesTillDatePayload) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type LoadEarlierMessagesTillDateRequest struct {
	state         protoimpl.MessageState              `protogen:"open.v1"`
	Metadata      *Metadata                           `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *LoadEarlierMessagesTillDatePayload `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadEarlierMessagesTillDateRequest) Reset() {
	*x = LoadEarlierMessagesTillDateRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadEarlierMessagesTillDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadEarlierMessagesTillDateRequest) ProtoMessage() {}

func (x *LoadEarlierMessagesTillDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadEarlierMessagesTillDateRequest.ProtoReflect.Descriptor instead.
func (*LoadEarlierMessagesTillDateRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{21}
}

func (x *LoadEarlierMessagesTillDateRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *LoadEarlierMessagesTillDateRequest) GetPayload() *LoadEarlierMessagesTillDatePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type LoadEarlierMessagesTillDateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Messages      []*MessageModel        `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadEarlierMessagesTillDateResponse) Reset() {
	*x = LoadEarlierMessagesTillDateResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadEarlierMessagesTillDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadEarlierMessagesTillDateResponse) ProtoMessage() {}

func (x *LoadEarlierMessagesTillDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadEarlierMessagesTillDateResponse.ProtoReflect.Descriptor instead.
func (*LoadEarlierMessagesTillDateResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{22}
}

func (x *LoadEarlierMessagesTillDateResponse) GetMessages() []*MessageModel {
	if x != nil {
		return x.Messages
	}
	return nil
}

type MessageModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                       // Identificador único da mensagem
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`                   // Texto da mensagem
	Timestamp     string                 `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`         // Timestamp ISO 8601
	QuotedMessage *MessageModel          `protobuf:"bytes,4,opt,name=quotedMessage,proto3" json:"quotedMessage,omitempty"` // Mensagem citada (subestrutura)
	IsFromMe      bool                   `protobuf:"varint,5,opt,name=isFromMe,proto3" json:"isFromMe,omitempty"`          // Indicador se a mensagem é do remetente
	ContactId     string                 `protobuf:"bytes,6,opt,name=contactId,proto3" json:"contactId,omitempty"`         // ID do contato
	FromId        string                 `protobuf:"bytes,7,opt,name=fromId,proto3" json:"fromId,omitempty"`               // ID do remetente
	Type          string                 `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`                   // Tipo da mensagem
	Preview       *FileModel             `protobuf:"bytes,9,opt,name=preview,proto3" json:"preview,omitempty"`             // Pré-visualização (se aplicável)
	File          *FileModel             `protobuf:"bytes,10,opt,name=file,proto3" json:"file,omitempty"`                  // Arquivo associado (se aplicável)
	Contact       *ContactModel          `protobuf:"bytes,11,opt,name=contact,proto3" json:"contact,omitempty"`            // Contato associado
	From          *ContactModel          `protobuf:"bytes,12,opt,name=from,proto3" json:"from,omitempty"`                  // Remetente da mensagem
	Ack           string                 `protobuf:"bytes,13,opt,name=ack,proto3" json:"ack,omitempty"`                    // Estado de confirmação
	Data          *MessageDataModel      `protobuf:"bytes,14,opt,name=data,proto3" json:"data,omitempty"`                  // Dados adicionais
	IsStatus      bool                   `protobuf:"varint,15,opt,name=isStatus,proto3" json:"isStatus,omitempty"`         // Mensagem é um status do whatsapp
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageModel) Reset() {
	*x = MessageModel{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel) ProtoMessage() {}

func (x *MessageModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel.ProtoReflect.Descriptor instead.
func (*MessageModel) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{23}
}

func (x *MessageModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MessageModel) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *MessageModel) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *MessageModel) GetQuotedMessage() *MessageModel {
	if x != nil {
		return x.QuotedMessage
	}
	return nil
}

func (x *MessageModel) GetIsFromMe() bool {
	if x != nil {
		return x.IsFromMe
	}
	return false
}

func (x *MessageModel) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

func (x *MessageModel) GetFromId() string {
	if x != nil {
		return x.FromId
	}
	return ""
}

func (x *MessageModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *MessageModel) GetPreview() *FileModel {
	if x != nil {
		return x.Preview
	}
	return nil
}

func (x *MessageModel) GetFile() *FileModel {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *MessageModel) GetContact() *ContactModel {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *MessageModel) GetFrom() *ContactModel {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *MessageModel) GetAck() string {
	if x != nil {
		return x.Ack
	}
	return ""
}

func (x *MessageModel) GetData() *MessageDataModel {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MessageModel) GetIsStatus() bool {
	if x != nil {
		return x.IsStatus
	}
	return false
}

type FileModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`           // URL do arquivo
	MimeType      string                 `protobuf:"bytes,2,opt,name=mimeType,proto3" json:"mimeType,omitempty"` // Tipo MIME
	Size          int64                  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`        // Tamanho do arquivo
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`         // Nome do arquivo
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileModel) Reset() {
	*x = FileModel{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileModel) ProtoMessage() {}

func (x *FileModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileModel.ProtoReflect.Descriptor instead.
func (*FileModel) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{24}
}

func (x *FileModel) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FileModel) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *FileModel) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FileModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ContactModel struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                              // ID do contato
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                          // Nome do contato
	ProfilePic       string                 `protobuf:"bytes,3,opt,name=profilePic,proto3" json:"profilePic,omitempty"`              // URL da foto de perfil
	IsGroup          bool                   `protobuf:"varint,4,opt,name=isGroup,proto3" json:"isGroup,omitempty"`                   // Indicador se o contato é um grupo
	AvatarUrl        string                 `protobuf:"bytes,5,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"`                // Correspondente a "avatarUrl", pode ser nulo ou vazio.
	IsBusiness       bool                   `protobuf:"varint,6,opt,name=isBusiness,proto3" json:"isBusiness,omitempty"`             // Correspondente a "isBusiness".
	IsContactBlocked bool                   `protobuf:"varint,7,opt,name=isContactBlocked,proto3" json:"isContactBlocked,omitempty"` // Correspondente a "isContactBlocked".
	IsEnterprise     bool                   `protobuf:"varint,8,opt,name=isEnterprise,proto3" json:"isEnterprise,omitempty"`         // Correspondente a "isEnterprise".
	IsMe             bool                   `protobuf:"varint,10,opt,name=isMe,proto3" json:"isMe,omitempty"`                        // Correspondente a "isMe".
	Number           string                 `protobuf:"bytes,12,opt,name=number,proto3" json:"number,omitempty"`                     // Correspondente a "number".
	ProfileName      string                 `protobuf:"bytes,13,opt,name=profileName,proto3" json:"profileName,omitempty"`           // Correspondente a "profileName".
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ContactModel) Reset() {
	*x = ContactModel{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactModel) ProtoMessage() {}

func (x *ContactModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactModel.ProtoReflect.Descriptor instead.
func (*ContactModel) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{25}
}

func (x *ContactModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ContactModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ContactModel) GetProfilePic() string {
	if x != nil {
		return x.ProfilePic
	}
	return ""
}

func (x *ContactModel) GetIsGroup() bool {
	if x != nil {
		return x.IsGroup
	}
	return false
}

func (x *ContactModel) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *ContactModel) GetIsBusiness() bool {
	if x != nil {
		return x.IsBusiness
	}
	return false
}

func (x *ContactModel) GetIsContactBlocked() bool {
	if x != nil {
		return x.IsContactBlocked
	}
	return false
}

func (x *ContactModel) GetIsEnterprise() bool {
	if x != nil {
		return x.IsEnterprise
	}
	return false
}

func (x *ContactModel) GetIsMe() bool {
	if x != nil {
		return x.IsMe
	}
	return false
}

func (x *ContactModel) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *ContactModel) GetProfileName() string {
	if x != nil {
		return x.ProfileName
	}
	return ""
}

type Location struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Lat           float32                `protobuf:"fixed32,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng           float32                `protobuf:"fixed32,2,opt,name=lng,proto3" json:"lng,omitempty"`
	MapPreviewUrl string                 `protobuf:"bytes,3,opt,name=mapPreviewUrl,proto3" json:"mapPreviewUrl,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Location) Reset() {
	*x = Location{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{26}
}

func (x *Location) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *Location) GetLng() float32 {
	if x != nil {
		return x.Lng
	}
	return 0
}

func (x *Location) GetMapPreviewUrl() string {
	if x != nil {
		return x.MapPreviewUrl
	}
	return ""
}

type CtwaContext struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ConversionSource string                 `protobuf:"bytes,1,opt,name=conversionSource,proto3" json:"conversionSource,omitempty"`
	Description      string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	IsSuspiciousLink string                 `protobuf:"bytes,3,opt,name=isSuspiciousLink,proto3" json:"isSuspiciousLink,omitempty"`
	MediaType        int32                  `protobuf:"varint,4,opt,name=mediaType,proto3" json:"mediaType,omitempty"`
	MediaUrl         string                 `protobuf:"bytes,5,opt,name=mediaUrl,proto3" json:"mediaUrl,omitempty"`
	SourceUrl        string                 `protobuf:"bytes,6,opt,name=sourceUrl,proto3" json:"sourceUrl,omitempty"`
	ThumbnailUrl     string                 `protobuf:"bytes,7,opt,name=thumbnailUrl,proto3" json:"thumbnailUrl,omitempty"`
	Title            string                 `protobuf:"bytes,8,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CtwaContext) Reset() {
	*x = CtwaContext{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CtwaContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CtwaContext) ProtoMessage() {}

func (x *CtwaContext) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CtwaContext.ProtoReflect.Descriptor instead.
func (*CtwaContext) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{27}
}

func (x *CtwaContext) GetConversionSource() string {
	if x != nil {
		return x.ConversionSource
	}
	return ""
}

func (x *CtwaContext) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CtwaContext) GetIsSuspiciousLink() string {
	if x != nil {
		return x.IsSuspiciousLink
	}
	return ""
}

func (x *CtwaContext) GetMediaType() int32 {
	if x != nil {
		return x.MediaType
	}
	return 0
}

func (x *CtwaContext) GetMediaUrl() string {
	if x != nil {
		return x.MediaUrl
	}
	return ""
}

func (x *CtwaContext) GetSourceUrl() string {
	if x != nil {
		return x.SourceUrl
	}
	return ""
}

func (x *CtwaContext) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *CtwaContext) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type MessageDataModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ack           int32                  `protobuf:"varint,1,opt,name=ack,proto3" json:"ack,omitempty"`                // Estado de confirmação
	Product       *anypb.Any             `protobuf:"bytes,2,opt,name=product,proto3" json:"product,omitempty"`         // Dados do produto (campo genérico)
	Vcard         string                 `protobuf:"bytes,3,opt,name=vcard,proto3" json:"vcard,omitempty"`             // Dados do vcard
	Vcards        []string               `protobuf:"bytes,4,rep,name=vcards,proto3" json:"vcards,omitempty"`           // Dados dos vcards
	Location      *Location              `protobuf:"bytes,5,opt,name=location,proto3" json:"location,omitempty"`       // Dados da localização
	CtwaContext   *CtwaContext           `protobuf:"bytes,6,opt,name=ctwaContext,proto3" json:"ctwaContext,omitempty"` // CTWA = Click to Whatsapp Ads
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageDataModel) Reset() {
	*x = MessageDataModel{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageDataModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageDataModel) ProtoMessage() {}

func (x *MessageDataModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageDataModel.ProtoReflect.Descriptor instead.
func (*MessageDataModel) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{28}
}

func (x *MessageDataModel) GetAck() int32 {
	if x != nil {
		return x.Ack
	}
	return 0
}

func (x *MessageDataModel) GetProduct() *anypb.Any {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *MessageDataModel) GetVcard() string {
	if x != nil {
		return x.Vcard
	}
	return ""
}

func (x *MessageDataModel) GetVcards() []string {
	if x != nil {
		return x.Vcards
	}
	return nil
}

func (x *MessageDataModel) GetLocation() *Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *MessageDataModel) GetCtwaContext() *CtwaContext {
	if x != nil {
		return x.CtwaContext
	}
	return nil
}

var File_common_grpc_common_proto_whatsapp_proto protoreflect.FileDescriptor

var file_common_grpc_common_proto_whatsapp_proto_rawDesc = []byte{
	0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x68, 0x61, 0x74, 0x73,
	0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2d, 0x0a, 0x0d,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x0a,
	0x08, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x04, 0x46, 0x69, 0x6c, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x53, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x73, 0x53, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x72,
	0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x73, 0x50, 0x74, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x61, 0x73, 0x50, 0x74, 0x74, 0x22, 0xaa, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x71, 0x75, 0x6f,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x48,
	0x00, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x22, 0x78, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x25, 0x0a,
	0x13, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x26, 0x0a, 0x14, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x31, 0x0a, 0x15,
	0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22,
	0x7c, 0x0a, 0x14, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x6b, 0x0a,
	0x15, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x10, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x16, 0x46, 0x6f,
	0x72, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x7e, 0x0a, 0x15, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x07, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x22, 0x70, 0x0a, 0x1c, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8c, 0x01, 0x0a, 0x1c, 0x53, 0x65, 0x6e, 0x64, 0x52,
	0x65, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x2f, 0x0a, 0x1d, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x50, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x26, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x78, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x07,
	0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x22, 0x6e, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x22, 0x71, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69,
	0x70, 0x61, 0x6e, 0x74, 0x73, 0x22, 0x60, 0x0a, 0x22, 0x4c, 0x6f, 0x61, 0x64, 0x45, 0x61, 0x72,
	0x6c, 0x69, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x54, 0x69, 0x6c, 0x6c,
	0x44, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x98, 0x01, 0x0a, 0x22, 0x4c, 0x6f, 0x61, 0x64,
	0x45, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x54,
	0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x07,
	0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x45, 0x61, 0x72, 0x6c, 0x69,
	0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x54, 0x69, 0x6c, 0x6c, 0x44, 0x61,
	0x74, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x22, 0x57, 0x0a, 0x23, 0x4c, 0x6f, 0x61, 0x64, 0x45, 0x61, 0x72, 0x6c, 0x69, 0x65,
	0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x54, 0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x08, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x22, 0xfc, 0x03, 0x0a, 0x0c,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3a,
	0x0a, 0x0d, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x71, 0x75, 0x6f,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73,
	0x46, 0x72, 0x6f, 0x6d, 0x4d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x46, 0x72, 0x6f, 0x6d, 0x4d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x72, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x72, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x2b, 0x0a, 0x07, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x25, 0x0a,
	0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x10,
	0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x63, 0x6b,
	0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x61, 0x0a, 0x09, 0x46, 0x69,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xc8, 0x02,
	0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x69, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x50,
	0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1c, 0x0a, 0x09,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x73,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x73,
	0x4d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x69, 0x73, 0x4d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x70, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6d, 0x61, 0x70, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x22, 0x99,
	0x02, 0x0a, 0x0b, 0x43, 0x74, 0x77, 0x61, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2a,
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x10,
	0x69, 0x73, 0x53, 0x75, 0x73, 0x70, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x4c, 0x69, 0x6e, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x73, 0x53, 0x75, 0x73, 0x70, 0x69, 0x63,
	0x69, 0x6f, 0x75, 0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x55,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x55,
	0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x22, 0x0a, 0x0c, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69,
	0x6c, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0xe7, 0x01, 0x0a, 0x10, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x10, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x63,
	0x6b, 0x12, 0x2e, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x63, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x63, 0x61, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x63, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x63, 0x61, 0x72, 0x64, 0x73, 0x12,
	0x2c, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a,
	0x0b, 0x63, 0x74, 0x77, 0x61, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x74, 0x77, 0x61,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x63, 0x74, 0x77, 0x61, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x42, 0x22, 0x5a, 0x20, 0x64, 0x69, 0x67, 0x69, 0x73, 0x61, 0x63, 0x2d,
	0x67, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_grpc_common_proto_whatsapp_proto_rawDescOnce sync.Once
	file_common_grpc_common_proto_whatsapp_proto_rawDescData = file_common_grpc_common_proto_whatsapp_proto_rawDesc
)

func file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP() []byte {
	file_common_grpc_common_proto_whatsapp_proto_rawDescOnce.Do(func() {
		file_common_grpc_common_proto_whatsapp_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_grpc_common_proto_whatsapp_proto_rawDescData)
	})
	return file_common_grpc_common_proto_whatsapp_proto_rawDescData
}

var file_common_grpc_common_proto_whatsapp_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_common_grpc_common_proto_whatsapp_proto_goTypes = []any{
	(*ActionPayload)(nil),                       // 0: common.ActionPayload
	(*ActionResponse)(nil),                      // 1: common.ActionResponse
	(*Metadata)(nil),                            // 2: common.Metadata
	(*File)(nil),                                // 3: common.File
	(*SendMessagePayload)(nil),                  // 4: common.SendMessagePayload
	(*SendMessageRequest)(nil),                  // 5: common.SendMessageRequest
	(*SendMessageResponse)(nil),                 // 6: common.SendMessageResponse
	(*RevokeMessagePayload)(nil),                // 7: common.RevokeMessagePayload
	(*RevokeMessageResponse)(nil),               // 8: common.RevokeMessageResponse
	(*RevokeMessageRequest)(nil),                // 9: common.RevokeMessageRequest
	(*ForwardMessagePayload)(nil),               // 10: common.ForwardMessagePayload
	(*ForwardMessageResponse)(nil),              // 11: common.ForwardMessageResponse
	(*ForwardMessageRequest)(nil),               // 12: common.ForwardMessageRequest
	(*SendReactionToMessagePayload)(nil),        // 13: common.SendReactionToMessagePayload
	(*SendReactionToMessageRequest)(nil),        // 14: common.SendReactionToMessageRequest
	(*SendReactionToMessageResponse)(nil),       // 15: common.SendReactionToMessageResponse
	(*CreateGroupPayload)(nil),                  // 16: common.CreateGroupPayload
	(*CreateGroupRequest)(nil),                  // 17: common.CreateGroupRequest
	(*CreateGroupParticipantResponse)(nil),      // 18: common.CreateGroupParticipantResponse
	(*CreateGroupResponse)(nil),                 // 19: common.CreateGroupResponse
	(*LoadEarlierMessagesTillDatePayload)(nil),  // 20: common.LoadEarlierMessagesTillDatePayload
	(*LoadEarlierMessagesTillDateRequest)(nil),  // 21: common.LoadEarlierMessagesTillDateRequest
	(*LoadEarlierMessagesTillDateResponse)(nil), // 22: common.LoadEarlierMessagesTillDateResponse
	(*MessageModel)(nil),                        // 23: common.MessageModel
	(*FileModel)(nil),                           // 24: common.FileModel
	(*ContactModel)(nil),                        // 25: common.ContactModel
	(*Location)(nil),                            // 26: common.Location
	(*CtwaContext)(nil),                         // 27: common.CtwaContext
	(*MessageDataModel)(nil),                    // 28: common.MessageDataModel
	(*anypb.Any)(nil),                           // 29: google.protobuf.Any
}
var file_common_grpc_common_proto_whatsapp_proto_depIdxs = []int32{
	3,  // 0: common.SendMessagePayload.file:type_name -> common.File
	2,  // 1: common.SendMessageRequest.metadata:type_name -> common.Metadata
	4,  // 2: common.SendMessageRequest.payload:type_name -> common.SendMessagePayload
	2,  // 3: common.RevokeMessageRequest.metadata:type_name -> common.Metadata
	7,  // 4: common.RevokeMessageRequest.payload:type_name -> common.RevokeMessagePayload
	2,  // 5: common.ForwardMessageRequest.metadata:type_name -> common.Metadata
	10, // 6: common.ForwardMessageRequest.payload:type_name -> common.ForwardMessagePayload
	2,  // 7: common.SendReactionToMessageRequest.metadata:type_name -> common.Metadata
	13, // 8: common.SendReactionToMessageRequest.payload:type_name -> common.SendReactionToMessagePayload
	2,  // 9: common.CreateGroupRequest.metadata:type_name -> common.Metadata
	16, // 10: common.CreateGroupRequest.payload:type_name -> common.CreateGroupPayload
	18, // 11: common.CreateGroupResponse.participants:type_name -> common.CreateGroupParticipantResponse
	2,  // 12: common.LoadEarlierMessagesTillDateRequest.metadata:type_name -> common.Metadata
	20, // 13: common.LoadEarlierMessagesTillDateRequest.payload:type_name -> common.LoadEarlierMessagesTillDatePayload
	23, // 14: common.LoadEarlierMessagesTillDateResponse.messages:type_name -> common.MessageModel
	23, // 15: common.MessageModel.quotedMessage:type_name -> common.MessageModel
	24, // 16: common.MessageModel.preview:type_name -> common.FileModel
	24, // 17: common.MessageModel.file:type_name -> common.FileModel
	25, // 18: common.MessageModel.contact:type_name -> common.ContactModel
	25, // 19: common.MessageModel.from:type_name -> common.ContactModel
	28, // 20: common.MessageModel.data:type_name -> common.MessageDataModel
	29, // 21: common.MessageDataModel.product:type_name -> google.protobuf.Any
	26, // 22: common.MessageDataModel.location:type_name -> common.Location
	27, // 23: common.MessageDataModel.ctwaContext:type_name -> common.CtwaContext
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_common_grpc_common_proto_whatsapp_proto_init() }
func file_common_grpc_common_proto_whatsapp_proto_init() {
	if File_common_grpc_common_proto_whatsapp_proto != nil {
		return
	}
	file_common_grpc_common_proto_whatsapp_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_grpc_common_proto_whatsapp_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_grpc_common_proto_whatsapp_proto_goTypes,
		DependencyIndexes: file_common_grpc_common_proto_whatsapp_proto_depIdxs,
		MessageInfos:      file_common_grpc_common_proto_whatsapp_proto_msgTypes,
	}.Build()
	File_common_grpc_common_proto_whatsapp_proto = out.File
	file_common_grpc_common_proto_whatsapp_proto_rawDesc = nil
	file_common_grpc_common_proto_whatsapp_proto_goTypes = nil
	file_common_grpc_common_proto_whatsapp_proto_depIdxs = nil
}
