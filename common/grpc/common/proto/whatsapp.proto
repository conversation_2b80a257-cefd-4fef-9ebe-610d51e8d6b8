syntax = "proto3";

package common;

import "google/protobuf/any.proto";

option go_package = "digisac-go/common/grpc/common;pb";

message ActionPayload {
  string serviceId = 1;
}

message ActionResponse {}

message Metadata {
  string serviceId = 1;
}

message File {
  string url = 1;
  string mimetype = 2;
  string name = 3;
  bool asSticker = 4;
  bool asDocument = 5;
  bool asPtt = 6;
}

/*****************************************
  SEND MESSAGE
******************************************/
message SendMessagePayload {
  string id = 1;
  string chatId = 2;
  string text = 3;
  string quotedMessageId = 4;
  optional File file = 5;
}

message SendMessageRequest {
  Metadata metadata = 1;
  SendMessagePayload payload = 2;
}

message SendMessageResponse {
  string id = 1;
}

/*****************************************
  REVOKE MESSAGE
******************************************/
message RevokeMessagePayload {
  string id = 1;
}

message RevokeMessageResponse {
  bool success = 1;
}

message RevokeMessageRequest {
  Metadata metadata = 1;
  RevokeMessagePayload payload = 2;
}


/*****************************************
  FORWARD MESSAGE
******************************************/
message ForwardMessagePayload {
  string id = 1;
  string chatId = 2;
  string forwardMessageId = 3;
}

message ForwardMessageResponse {
  string id = 1;
}

message ForwardMessageRequest {
  Metadata metadata = 1;
  ForwardMessagePayload payload = 2;
}

/*****************************************
  SEND REACTION TO MESSAGE
******************************************/
message SendReactionToMessagePayload {
  string chatId = 1;
  string messageId = 2;
  string reaction = 3;
}

message SendReactionToMessageRequest {
  Metadata metadata = 1;
  SendReactionToMessagePayload payload = 2;
}

message SendReactionToMessageResponse {
  string id = 1;
}

/*****************************************
  CREATE GROUP
******************************************/
message CreateGroupPayload {
  string name = 1;
  repeated string participantIds = 2;
}

message CreateGroupRequest {
  Metadata metadata = 1;
  CreateGroupPayload payload = 2;
}

message CreateGroupParticipantResponse {
  string contactId = 1;
  bool success = 2;
  string error = 3;
}

message CreateGroupResponse {
  string id = 1;
  repeated CreateGroupParticipantResponse participants = 2;
}

/*****************************************
  LOAD EARLIER MESSAGES TILL DATE
******************************************/

message LoadEarlierMessagesTillDatePayload {
  string contactId = 1;
  int64 timestamp = 2;
}

message LoadEarlierMessagesTillDateRequest {
  Metadata metadata = 1;
  LoadEarlierMessagesTillDatePayload payload = 2;
}

message LoadEarlierMessagesTillDateResponse {
  repeated MessageModel messages = 1;
}

/*****************************************
  WPP MODELS
******************************************/

message MessageModel {
    string id = 1;                         // Identificador único da mensagem
    string text = 2;                       // Texto da mensagem
    string timestamp = 3;                  // Timestamp ISO 8601
    MessageModel quotedMessage = 4;  // Mensagem citada (subestrutura)
    bool isFromMe = 5;                     // Indicador se a mensagem é do remetente
    string contactId = 6;                  // ID do contato
    string fromId = 7;                     // ID do remetente
    string type = 8;                       // Tipo da mensagem
    FileModel preview = 9;                 // Pré-visualização (se aplicável)
    FileModel file = 10;                   // Arquivo associado (se aplicável)
    ContactModel contact = 11;             // Contato associado
    ContactModel from = 12;                // Remetente da mensagem
    string ack = 13;                       // Estado de confirmação
    MessageDataModel data = 14;            // Dados adicionais
    bool isStatus = 15;                    // Mensagem é um status do whatsapp
}

message FileModel {
    string url = 1;                       // URL do arquivo
    string mimeType = 2;                  // Tipo MIME
    int64 size = 3;                       // Tamanho do arquivo
    string name = 4;                      // Nome do arquivo
}

message ContactModel {
    string id = 1;                        // ID do contato
    string name = 2;                      // Nome do contato
    string profilePic = 3;                // URL da foto de perfil
    bool isGroup = 4;                     // Indicador se o contato é um grupo
    string avatarUrl = 5;                 // Correspondente a "avatarUrl", pode ser nulo ou vazio.
    bool isBusiness = 6;                  // Correspondente a "isBusiness".
    bool isContactBlocked = 7;            // Correspondente a "isContactBlocked".
    bool isEnterprise = 8;                // Correspondente a "isEnterprise".
    bool isMe = 10;                       // Correspondente a "isMe".
    string number = 12;                   // Correspondente a "number".
    string profileName = 13;              // Correspondente a "profileName".
}

message Location {
  float lat = 1;
  float lng = 2; 
  string mapPreviewUrl = 3;
}

message CtwaContext {
  string conversionSource = 1;
  string description = 2;
  string isSuspiciousLink = 3;
  int32 mediaType = 4;
  string mediaUrl = 5;
  string sourceUrl = 6;
  string thumbnailUrl = 7;
  string title = 8;
  }

message MessageDataModel {
    int32 ack = 1;                         // Estado de confirmação
    google.protobuf.Any product = 2;       // Dados do produto (campo genérico)
    string vcard = 3;                      // Dados do vcard
    repeated string  vcards = 4;           // Dados dos vcards
    Location location = 5;                 // Dados da localização
    CtwaContext ctwaContext = 6;           // CTWA = Click to Whatsapp Ads
}
