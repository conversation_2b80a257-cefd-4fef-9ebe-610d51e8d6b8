syntax = "proto3";

package api;

option go_package = "digisac-go/common/grpc/api;pb";

import "common/grpc/common/proto/whatsapp.proto";
import "common/grpc/common/proto/service.proto";
import "common/grpc/common/proto/account.proto";
import "google/protobuf/empty.proto";

service PublicService {
  rpc SendMessage (common.SendMessageRequest) returns (common.SendMessageResponse);
  rpc RevokeMessage (common.RevokeMessageRequest) returns (common.RevokeMessageResponse);
  rpc ForwardMessage (common.ForwardMessageRequest) returns (common.ForwardMessageResponse);
  rpc SendReactionToMessage (common.SendReactionToMessageRequest) returns (common.SendReactionToMessageResponse);
  rpc CreateGroup (common.CreateGroupRequest) returns (common.CreateGroupResponse);
  rpc AddGroupParticipants (common.CreateGroupRequest) returns (common.CreateGroupResponse);
  rpc RemoveGroupParticipants (common.CreateGroupRequest) returns (common.CreateGroupResponse);
  rpc LoadEarlierMessagesTillDate (common.LoadEarlierMessagesTillDateRequest) returns (common.LoadEarlierMessagesTillDateResponse);

  rpc Start (common.ActionPayload) returns (common.ActionResponse);
  rpc Stop (common.ActionPayload) returns (common.ActionResponse);
  rpc Restart (common.ActionPayload) returns (common.ActionResponse);
  rpc Logout (common.ActionPayload) returns (common.ActionResponse);
  rpc Takeover (common.ActionPayload) returns (common.ActionResponse);

  rpc GetAccount(google.protobuf.Empty) returns (common.AccountResponse);
  rpc SetAccountWebhook(common.SetAccountWebhookUrlRequest) returns (google.protobuf.Empty);

  rpc CreateService(common.CreateServiceRequest) returns (common.CreateServiceResponse);
  rpc GetService(common.GetServiceRequest) returns (common.GetServiceResponse);
  rpc ListServices(common.ListServicesRequest) returns (common.ListServicesResponse);
  rpc UpdateService(common.UpdateServiceRequest) returns (common.UpdateServiceResponse);
  rpc DeleteService(common.DeleteServiceRequest) returns (common.DeleteServiceResponse);
}