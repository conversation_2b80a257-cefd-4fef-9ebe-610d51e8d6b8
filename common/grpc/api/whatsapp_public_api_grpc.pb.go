// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: common/grpc/api/proto/whatsapp_public_api.proto

package pb

import (
	context "context"
	common "digisac-go/common/grpc/common"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PublicService_SendMessage_FullMethodName                 = "/api.PublicService/SendMessage"
	PublicService_RevokeMessage_FullMethodName               = "/api.PublicService/RevokeMessage"
	PublicService_ForwardMessage_FullMethodName              = "/api.PublicService/ForwardMessage"
	PublicService_SendReactionToMessage_FullMethodName       = "/api.PublicService/SendReactionToMessage"
	PublicService_CreateGroup_FullMethodName                 = "/api.PublicService/CreateGroup"
	PublicService_AddGroupParticipants_FullMethodName        = "/api.PublicService/AddGroupParticipants"
	PublicService_RemoveGroupParticipants_FullMethodName     = "/api.PublicService/RemoveGroupParticipants"
	PublicService_LoadEarlierMessagesTillDate_FullMethodName = "/api.PublicService/LoadEarlierMessagesTillDate"
	PublicService_Start_FullMethodName                       = "/api.PublicService/Start"
	PublicService_Stop_FullMethodName                        = "/api.PublicService/Stop"
	PublicService_Restart_FullMethodName                     = "/api.PublicService/Restart"
	PublicService_Logout_FullMethodName                      = "/api.PublicService/Logout"
	PublicService_Takeover_FullMethodName                    = "/api.PublicService/Takeover"
	PublicService_GetAccount_FullMethodName                  = "/api.PublicService/GetAccount"
	PublicService_SetAccountWebhook_FullMethodName           = "/api.PublicService/SetAccountWebhook"
	PublicService_CreateService_FullMethodName               = "/api.PublicService/CreateService"
	PublicService_GetService_FullMethodName                  = "/api.PublicService/GetService"
	PublicService_ListServices_FullMethodName                = "/api.PublicService/ListServices"
	PublicService_UpdateService_FullMethodName               = "/api.PublicService/UpdateService"
	PublicService_DeleteService_FullMethodName               = "/api.PublicService/DeleteService"
)

// PublicServiceClient is the client API for PublicService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PublicServiceClient interface {
	SendMessage(ctx context.Context, in *common.SendMessageRequest, opts ...grpc.CallOption) (*common.SendMessageResponse, error)
	RevokeMessage(ctx context.Context, in *common.RevokeMessageRequest, opts ...grpc.CallOption) (*common.RevokeMessageResponse, error)
	ForwardMessage(ctx context.Context, in *common.ForwardMessageRequest, opts ...grpc.CallOption) (*common.ForwardMessageResponse, error)
	SendReactionToMessage(ctx context.Context, in *common.SendReactionToMessageRequest, opts ...grpc.CallOption) (*common.SendReactionToMessageResponse, error)
	CreateGroup(ctx context.Context, in *common.CreateGroupRequest, opts ...grpc.CallOption) (*common.CreateGroupResponse, error)
	AddGroupParticipants(ctx context.Context, in *common.CreateGroupRequest, opts ...grpc.CallOption) (*common.CreateGroupResponse, error)
	RemoveGroupParticipants(ctx context.Context, in *common.CreateGroupRequest, opts ...grpc.CallOption) (*common.CreateGroupResponse, error)
	LoadEarlierMessagesTillDate(ctx context.Context, in *common.LoadEarlierMessagesTillDateRequest, opts ...grpc.CallOption) (*common.LoadEarlierMessagesTillDateResponse, error)
	Start(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error)
	Stop(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error)
	Restart(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error)
	Logout(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error)
	Takeover(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error)
	GetAccount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*common.AccountResponse, error)
	SetAccountWebhook(ctx context.Context, in *common.SetAccountWebhookUrlRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CreateService(ctx context.Context, in *common.CreateServiceRequest, opts ...grpc.CallOption) (*common.CreateServiceResponse, error)
	GetService(ctx context.Context, in *common.GetServiceRequest, opts ...grpc.CallOption) (*common.GetServiceResponse, error)
	ListServices(ctx context.Context, in *common.ListServicesRequest, opts ...grpc.CallOption) (*common.ListServicesResponse, error)
	UpdateService(ctx context.Context, in *common.UpdateServiceRequest, opts ...grpc.CallOption) (*common.UpdateServiceResponse, error)
	DeleteService(ctx context.Context, in *common.DeleteServiceRequest, opts ...grpc.CallOption) (*common.DeleteServiceResponse, error)
}

type publicServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPublicServiceClient(cc grpc.ClientConnInterface) PublicServiceClient {
	return &publicServiceClient{cc}
}

func (c *publicServiceClient) SendMessage(ctx context.Context, in *common.SendMessageRequest, opts ...grpc.CallOption) (*common.SendMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.SendMessageResponse)
	err := c.cc.Invoke(ctx, PublicService_SendMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) RevokeMessage(ctx context.Context, in *common.RevokeMessageRequest, opts ...grpc.CallOption) (*common.RevokeMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.RevokeMessageResponse)
	err := c.cc.Invoke(ctx, PublicService_RevokeMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) ForwardMessage(ctx context.Context, in *common.ForwardMessageRequest, opts ...grpc.CallOption) (*common.ForwardMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.ForwardMessageResponse)
	err := c.cc.Invoke(ctx, PublicService_ForwardMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) SendReactionToMessage(ctx context.Context, in *common.SendReactionToMessageRequest, opts ...grpc.CallOption) (*common.SendReactionToMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.SendReactionToMessageResponse)
	err := c.cc.Invoke(ctx, PublicService_SendReactionToMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) CreateGroup(ctx context.Context, in *common.CreateGroupRequest, opts ...grpc.CallOption) (*common.CreateGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.CreateGroupResponse)
	err := c.cc.Invoke(ctx, PublicService_CreateGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) AddGroupParticipants(ctx context.Context, in *common.CreateGroupRequest, opts ...grpc.CallOption) (*common.CreateGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.CreateGroupResponse)
	err := c.cc.Invoke(ctx, PublicService_AddGroupParticipants_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) RemoveGroupParticipants(ctx context.Context, in *common.CreateGroupRequest, opts ...grpc.CallOption) (*common.CreateGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.CreateGroupResponse)
	err := c.cc.Invoke(ctx, PublicService_RemoveGroupParticipants_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) LoadEarlierMessagesTillDate(ctx context.Context, in *common.LoadEarlierMessagesTillDateRequest, opts ...grpc.CallOption) (*common.LoadEarlierMessagesTillDateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.LoadEarlierMessagesTillDateResponse)
	err := c.cc.Invoke(ctx, PublicService_LoadEarlierMessagesTillDate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) Start(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.ActionResponse)
	err := c.cc.Invoke(ctx, PublicService_Start_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) Stop(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.ActionResponse)
	err := c.cc.Invoke(ctx, PublicService_Stop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) Restart(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.ActionResponse)
	err := c.cc.Invoke(ctx, PublicService_Restart_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) Logout(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.ActionResponse)
	err := c.cc.Invoke(ctx, PublicService_Logout_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) Takeover(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.ActionResponse)
	err := c.cc.Invoke(ctx, PublicService_Takeover_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) GetAccount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*common.AccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.AccountResponse)
	err := c.cc.Invoke(ctx, PublicService_GetAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) SetAccountWebhook(ctx context.Context, in *common.SetAccountWebhookUrlRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PublicService_SetAccountWebhook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) CreateService(ctx context.Context, in *common.CreateServiceRequest, opts ...grpc.CallOption) (*common.CreateServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.CreateServiceResponse)
	err := c.cc.Invoke(ctx, PublicService_CreateService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) GetService(ctx context.Context, in *common.GetServiceRequest, opts ...grpc.CallOption) (*common.GetServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.GetServiceResponse)
	err := c.cc.Invoke(ctx, PublicService_GetService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) ListServices(ctx context.Context, in *common.ListServicesRequest, opts ...grpc.CallOption) (*common.ListServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.ListServicesResponse)
	err := c.cc.Invoke(ctx, PublicService_ListServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) UpdateService(ctx context.Context, in *common.UpdateServiceRequest, opts ...grpc.CallOption) (*common.UpdateServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.UpdateServiceResponse)
	err := c.cc.Invoke(ctx, PublicService_UpdateService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicServiceClient) DeleteService(ctx context.Context, in *common.DeleteServiceRequest, opts ...grpc.CallOption) (*common.DeleteServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.DeleteServiceResponse)
	err := c.cc.Invoke(ctx, PublicService_DeleteService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PublicServiceServer is the server API for PublicService service.
// All implementations must embed UnimplementedPublicServiceServer
// for forward compatibility.
type PublicServiceServer interface {
	SendMessage(context.Context, *common.SendMessageRequest) (*common.SendMessageResponse, error)
	RevokeMessage(context.Context, *common.RevokeMessageRequest) (*common.RevokeMessageResponse, error)
	ForwardMessage(context.Context, *common.ForwardMessageRequest) (*common.ForwardMessageResponse, error)
	SendReactionToMessage(context.Context, *common.SendReactionToMessageRequest) (*common.SendReactionToMessageResponse, error)
	CreateGroup(context.Context, *common.CreateGroupRequest) (*common.CreateGroupResponse, error)
	AddGroupParticipants(context.Context, *common.CreateGroupRequest) (*common.CreateGroupResponse, error)
	RemoveGroupParticipants(context.Context, *common.CreateGroupRequest) (*common.CreateGroupResponse, error)
	LoadEarlierMessagesTillDate(context.Context, *common.LoadEarlierMessagesTillDateRequest) (*common.LoadEarlierMessagesTillDateResponse, error)
	Start(context.Context, *common.ActionPayload) (*common.ActionResponse, error)
	Stop(context.Context, *common.ActionPayload) (*common.ActionResponse, error)
	Restart(context.Context, *common.ActionPayload) (*common.ActionResponse, error)
	Logout(context.Context, *common.ActionPayload) (*common.ActionResponse, error)
	Takeover(context.Context, *common.ActionPayload) (*common.ActionResponse, error)
	GetAccount(context.Context, *emptypb.Empty) (*common.AccountResponse, error)
	SetAccountWebhook(context.Context, *common.SetAccountWebhookUrlRequest) (*emptypb.Empty, error)
	CreateService(context.Context, *common.CreateServiceRequest) (*common.CreateServiceResponse, error)
	GetService(context.Context, *common.GetServiceRequest) (*common.GetServiceResponse, error)
	ListServices(context.Context, *common.ListServicesRequest) (*common.ListServicesResponse, error)
	UpdateService(context.Context, *common.UpdateServiceRequest) (*common.UpdateServiceResponse, error)
	DeleteService(context.Context, *common.DeleteServiceRequest) (*common.DeleteServiceResponse, error)
	mustEmbedUnimplementedPublicServiceServer()
}

// UnimplementedPublicServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPublicServiceServer struct{}

func (UnimplementedPublicServiceServer) SendMessage(context.Context, *common.SendMessageRequest) (*common.SendMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedPublicServiceServer) RevokeMessage(context.Context, *common.RevokeMessageRequest) (*common.RevokeMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeMessage not implemented")
}
func (UnimplementedPublicServiceServer) ForwardMessage(context.Context, *common.ForwardMessageRequest) (*common.ForwardMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForwardMessage not implemented")
}
func (UnimplementedPublicServiceServer) SendReactionToMessage(context.Context, *common.SendReactionToMessageRequest) (*common.SendReactionToMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReactionToMessage not implemented")
}
func (UnimplementedPublicServiceServer) CreateGroup(context.Context, *common.CreateGroupRequest) (*common.CreateGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGroup not implemented")
}
func (UnimplementedPublicServiceServer) AddGroupParticipants(context.Context, *common.CreateGroupRequest) (*common.CreateGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGroupParticipants not implemented")
}
func (UnimplementedPublicServiceServer) RemoveGroupParticipants(context.Context, *common.CreateGroupRequest) (*common.CreateGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveGroupParticipants not implemented")
}
func (UnimplementedPublicServiceServer) LoadEarlierMessagesTillDate(context.Context, *common.LoadEarlierMessagesTillDateRequest) (*common.LoadEarlierMessagesTillDateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadEarlierMessagesTillDate not implemented")
}
func (UnimplementedPublicServiceServer) Start(context.Context, *common.ActionPayload) (*common.ActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Start not implemented")
}
func (UnimplementedPublicServiceServer) Stop(context.Context, *common.ActionPayload) (*common.ActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Stop not implemented")
}
func (UnimplementedPublicServiceServer) Restart(context.Context, *common.ActionPayload) (*common.ActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Restart not implemented")
}
func (UnimplementedPublicServiceServer) Logout(context.Context, *common.ActionPayload) (*common.ActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedPublicServiceServer) Takeover(context.Context, *common.ActionPayload) (*common.ActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Takeover not implemented")
}
func (UnimplementedPublicServiceServer) GetAccount(context.Context, *emptypb.Empty) (*common.AccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedPublicServiceServer) SetAccountWebhook(context.Context, *common.SetAccountWebhookUrlRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAccountWebhook not implemented")
}
func (UnimplementedPublicServiceServer) CreateService(context.Context, *common.CreateServiceRequest) (*common.CreateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateService not implemented")
}
func (UnimplementedPublicServiceServer) GetService(context.Context, *common.GetServiceRequest) (*common.GetServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetService not implemented")
}
func (UnimplementedPublicServiceServer) ListServices(context.Context, *common.ListServicesRequest) (*common.ListServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServices not implemented")
}
func (UnimplementedPublicServiceServer) UpdateService(context.Context, *common.UpdateServiceRequest) (*common.UpdateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateService not implemented")
}
func (UnimplementedPublicServiceServer) DeleteService(context.Context, *common.DeleteServiceRequest) (*common.DeleteServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteService not implemented")
}
func (UnimplementedPublicServiceServer) mustEmbedUnimplementedPublicServiceServer() {}
func (UnimplementedPublicServiceServer) testEmbeddedByValue()                       {}

// UnsafePublicServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PublicServiceServer will
// result in compilation errors.
type UnsafePublicServiceServer interface {
	mustEmbedUnimplementedPublicServiceServer()
}

func RegisterPublicServiceServer(s grpc.ServiceRegistrar, srv PublicServiceServer) {
	// If the following call pancis, it indicates UnimplementedPublicServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PublicService_ServiceDesc, srv)
}

func _PublicService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.SendMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_SendMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).SendMessage(ctx, req.(*common.SendMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_RevokeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.RevokeMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).RevokeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_RevokeMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).RevokeMessage(ctx, req.(*common.RevokeMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_ForwardMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.ForwardMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).ForwardMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_ForwardMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).ForwardMessage(ctx, req.(*common.ForwardMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_SendReactionToMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.SendReactionToMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).SendReactionToMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_SendReactionToMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).SendReactionToMessage(ctx, req.(*common.SendReactionToMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_CreateGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.CreateGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).CreateGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_CreateGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).CreateGroup(ctx, req.(*common.CreateGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_AddGroupParticipants_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.CreateGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).AddGroupParticipants(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_AddGroupParticipants_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).AddGroupParticipants(ctx, req.(*common.CreateGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_RemoveGroupParticipants_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.CreateGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).RemoveGroupParticipants(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_RemoveGroupParticipants_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).RemoveGroupParticipants(ctx, req.(*common.CreateGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_LoadEarlierMessagesTillDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.LoadEarlierMessagesTillDateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).LoadEarlierMessagesTillDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_LoadEarlierMessagesTillDate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).LoadEarlierMessagesTillDate(ctx, req.(*common.LoadEarlierMessagesTillDateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_Start_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.ActionPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).Start(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_Start_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).Start(ctx, req.(*common.ActionPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_Stop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.ActionPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).Stop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_Stop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).Stop(ctx, req.(*common.ActionPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_Restart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.ActionPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).Restart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_Restart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).Restart(ctx, req.(*common.ActionPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.ActionPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_Logout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).Logout(ctx, req.(*common.ActionPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_Takeover_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.ActionPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).Takeover(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_Takeover_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).Takeover(ctx, req.(*common.ActionPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).GetAccount(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_SetAccountWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.SetAccountWebhookUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).SetAccountWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_SetAccountWebhook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).SetAccountWebhook(ctx, req.(*common.SetAccountWebhookUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_CreateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.CreateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).CreateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_CreateService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).CreateService(ctx, req.(*common.CreateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_GetService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.GetServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).GetService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_GetService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).GetService(ctx, req.(*common.GetServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_ListServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.ListServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).ListServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_ListServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).ListServices(ctx, req.(*common.ListServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_UpdateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.UpdateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).UpdateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_UpdateService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).UpdateService(ctx, req.(*common.UpdateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicService_DeleteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.DeleteServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicServiceServer).DeleteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicService_DeleteService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicServiceServer).DeleteService(ctx, req.(*common.DeleteServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PublicService_ServiceDesc is the grpc.ServiceDesc for PublicService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PublicService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.PublicService",
	HandlerType: (*PublicServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendMessage",
			Handler:    _PublicService_SendMessage_Handler,
		},
		{
			MethodName: "RevokeMessage",
			Handler:    _PublicService_RevokeMessage_Handler,
		},
		{
			MethodName: "ForwardMessage",
			Handler:    _PublicService_ForwardMessage_Handler,
		},
		{
			MethodName: "SendReactionToMessage",
			Handler:    _PublicService_SendReactionToMessage_Handler,
		},
		{
			MethodName: "CreateGroup",
			Handler:    _PublicService_CreateGroup_Handler,
		},
		{
			MethodName: "AddGroupParticipants",
			Handler:    _PublicService_AddGroupParticipants_Handler,
		},
		{
			MethodName: "RemoveGroupParticipants",
			Handler:    _PublicService_RemoveGroupParticipants_Handler,
		},
		{
			MethodName: "LoadEarlierMessagesTillDate",
			Handler:    _PublicService_LoadEarlierMessagesTillDate_Handler,
		},
		{
			MethodName: "Start",
			Handler:    _PublicService_Start_Handler,
		},
		{
			MethodName: "Stop",
			Handler:    _PublicService_Stop_Handler,
		},
		{
			MethodName: "Restart",
			Handler:    _PublicService_Restart_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _PublicService_Logout_Handler,
		},
		{
			MethodName: "Takeover",
			Handler:    _PublicService_Takeover_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _PublicService_GetAccount_Handler,
		},
		{
			MethodName: "SetAccountWebhook",
			Handler:    _PublicService_SetAccountWebhook_Handler,
		},
		{
			MethodName: "CreateService",
			Handler:    _PublicService_CreateService_Handler,
		},
		{
			MethodName: "GetService",
			Handler:    _PublicService_GetService_Handler,
		},
		{
			MethodName: "ListServices",
			Handler:    _PublicService_ListServices_Handler,
		},
		{
			MethodName: "UpdateService",
			Handler:    _PublicService_UpdateService_Handler,
		},
		{
			MethodName: "DeleteService",
			Handler:    _PublicService_DeleteService_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "common/grpc/api/proto/whatsapp_public_api.proto",
}
